const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticate, requireAdmin } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// 所有管理员路由都需要认证和管理员权限
router.use(authenticate);
router.use(requireAdmin);

// 获取统计数据
router.get('/stats/users', async (req, res) => {
  try {
    const count = await prisma.user.count();
    res.json({ count });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get('/stats/series', async (req, res) => {
  try {
    const count = await prisma.series.count();
    res.json({ count });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get('/stats/pets', async (req, res) => {
  try {
    const count = await prisma.pet.count();
    res.json({ count });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get('/stats/draws', async (req, res) => {
  try {
    const count = await prisma.drawEvent.count();
    res.json({ count });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取所有用户
router.get('/users', async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        points: true,
        role: true,
        avatar: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 管理系列
router.get('/series', async (req, res) => {
  try {
    // TODO: 获取所有系列（包括禁用的）
    res.json({ 
      message: '获取管理系列功能开发中...',
      data: [] 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 创建新系列
router.post('/series', async (req, res) => {
  try {
    // TODO: 创建新系列
    res.json({ 
      message: '创建系列功能开发中...',
      data: null 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新系列
router.put('/series/:id', async (req, res) => {
  try {
    const { id } = req.params;
    // TODO: 更新系列
    res.json({ 
      message: `更新系列${id}功能开发中...`,
      data: null 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除系列
router.delete('/series/:id', async (req, res) => {
  try {
    const { id } = req.params;
    // TODO: 删除系列
    res.json({ 
      message: `删除系列${id}功能开发中...`,
      data: null 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
