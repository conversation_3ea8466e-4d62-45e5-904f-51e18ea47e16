{"name": "mystibox-backend", "version": "1.0.0", "description": "MystiBox后端API服务", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": ["mystibox", "api", "express", "nodejs"], "author": "MystiBox Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}