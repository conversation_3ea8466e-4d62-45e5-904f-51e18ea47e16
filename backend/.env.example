# MystiBox 后端环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# 数据库配置
# SQLite数据库文件路径 (开发环境推荐)
DATABASE_URL="file:./dev.db"

# 生产环境可使用PostgreSQL
# DATABASE_URL="postgresql://username:password@localhost:5432/mystibox"

# JWT密钥配置
# 用于生成和验证JWT令牌的密钥
# 生产环境中请使用强随机字符串
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# 服务器配置
# 后端服务器运行端口
PORT=3003
NODE_ENV="development"

# CORS配置
# 前端应用的URL，用于CORS设置
FRONTEND_URL="http://localhost:3000"

# 管理员配置 (可选)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123456"

# 可选配置

# 日志级别
# LOG_LEVEL="info"

# 文件上传配置
# MAX_FILE_SIZE="5mb"
# UPLOAD_PATH="./uploads"

# 邮件配置 (如果需要邮件功能)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# Redis配置 (如果使用Redis缓存)
# REDIS_URL="redis://localhost:6379"

# 生产环境配置示例
# NODE_ENV="production"
# DATABASE_URL="postgresql://username:password@localhost:5432/mystibox"
# JWT_SECRET="your-production-jwt-secret-very-long-and-random"
# PORT=3003
# FRONTEND_URL="https://your-domain.com"
