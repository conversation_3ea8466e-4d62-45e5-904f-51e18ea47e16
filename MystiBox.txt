《宠物盲盒》Web 应用开发计划书 V3.0 (泰坦版)
文档版本： 3.0
修订日期： 2025年7月24日
核心目标： 打造一份不仅能满足所有评分标准，更能构建一个情感丰富、技术扎实、体验卓越的产品的终极开发蓝图。
文档导览
Part 1: 项目哲学与艺术监制圣经 - 我们首先定义项目的灵魂和外观。这是您与美术设计师沟通的桥梁，确保每一个像素都充满魅力。
Part 2: 系统架构与技术基石 - 搭建项目的骨架。我们将详细规划技术选型、目录结构和核心模块，为高效开发奠定基础。
Part 3: 功能蓝图与实现细节 (核心章节) - 本文档的核心。我们将逐一拆解评分标准中的每一项功能要求，并提供用户故事、UI/UX 流程、前后端组件/逻辑、API 接口和数据库交互的超详细描述。
Part 4: 数据库结构设计 - 项目的数据核心。提供完整的、可直接执行的 SQL Schema，包含表、字段、关系和索引的详尽说明。
Part 5: 开发、测试与部署 (DevOps) - 确保项目工程化、规范化。涵盖从代码提交到线上部署的全流程自动化方案，直接满足评分要求。
Part 6: 项目路线图与里程碑 - 将庞大的项目分解为可执行的阶段，让您清晰地知道每一步该做什么。
Part 1: 项目哲学与艺术监制圣经 (The Art Bible)
V2.0 方案评估与 V3.0 修正：
V2.0 不足： "Cozy & Playful Manga" 是一个很好的方向，但过于抽象，无法直接指导美术设计。它没有提供具体的执行标准。
V3.0 改进： 我们将建立一套可量化的、具体的艺术规范，形成一份“艺术圣经”，让您能够像专业制作人一样监制美术资产。
1.1 项目核心情感体验
我们的核心不再仅仅是“期待、惊喜、陪伴”，而是更具象的：“开启一个充满温暖与奇遇的掌上宠物世界”。
1.2 视觉风格定义：暖彩绘本 (Warm Picture-Book) x 轻日系漫画 (Light Manga)
色彩理论与规范 (Color Palette):
主色调 (天空与海洋): #87CEEB (柔和天空蓝) - 大面积背景、导航栏。营造宁静、广阔、纯净的感觉。
辅助色 (阳光与活力): #FFDAB9 (蜜桃橘) - 卡片、高亮区域、次要按钮。带来温暖、治愈、友好的感觉。
当然，让我们立即从中断的地方继续，并以前所未有的深度和广度，将这份 3.0 计划书推向极致。

...接上文...

强调/行动色 (Call-to-Action): #FF6F61 (活力珊瑚粉) - 用于所有关键操作按钮（如“抽取！”、“购买”、“确认”），在柔和的背景中形成鲜明而友好的视觉焦点。

Hover 状态: #FF8B82 (颜色变亮)

Active/Click 状态: #E66357 (颜色变深)

中性色系 (Neutrals):

主背景: #F9F9F9 - 近乎白色，但比纯白更柔和，降低视觉疲劳。

一级文本/标题: #333333 (深炭灰) - 保证最佳可读性。

二级文本/描述: #666666 (中度灰) - 用于辅助信息和描述性文字。

禁用/占位符文本: #AAAAAA (浅灰) - 用于禁用的元素或输入框提示。

边框/分割线: #EAEAEA - 细微且不突兀的元素边界。

状态色 (System Feedback):

成功 (Success): #28a745 (柔和森林绿) - 用于操作成功提示。

警告 (Warning): #ffc107 (温暖琥珀黄) - 用于需要用户注意的非阻塞性提示。

错误 (Error): #dc3545 (柔和番茄红) - 用于表单验证失败、API 错误等。

稀有度核心色板 (Rarity Color System) - [关键艺术规范]

这是贯穿整个应用的核心视觉语言，用于卡片边框、背景辉光、抽取动画、稀有度标签等。

N (Normal): #B0C4DE (柔和石灰) - 代表普通、常见。

R (Rare): #89CFF0 (星光蓝) - 代表稀有、纯净。

SR (Super Rare): #9370DB (神秘紫罗兰) - 代表超稀有、充满神秘感。

SSR (Super Super Rare): #FFD700 (辉煌流金) - 代表顶级的、传说中的稀有度。

UR (Ultra Rare - 隐藏等级): linear-gradient(45deg, #ff2525, #ff7b00, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff) (虹光幻彩) - 用于极少数特殊活动宠物，提供极致的视觉冲击力。

1.3 字体规范 (Typography)

字体选型: 继续沿用 Nunito (标题) 和 Lato (正文) 的组合，它们可通过 Google Fonts 免费获取。

字体引入: 在 Vue 项目的 index.html 的 <head> 标签中，使用 <link> 标签异步引入字体，避免阻塞渲染。

Generated html
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&family=Nunito:wght@700;900&display=swap" rel="stylesheet">


字体层级与应用 (Typographic Scale):

用途	字体	字重 (Weight)	字号 (Size)	行高 (Line Height)	示例
H1 - 页面主标题	Nunito	900 (Black)	36px	48px	欢迎来到宠物世界
H2 - 区域大标题	Nunito	700 (Bold)	28px	40px	盲盒商店
H3 - 卡片/模块标题	Nunito	700 (Bold)	20px	28px	森林系列
Body - 正文/描述	Lato	400 (Regular)	16px	24px	这是一段宠物的详细介绍...
Strong - 正文强调	Lato	700 (Bold)	16px	24px	重要提示
Button - 按钮文字	Nunito	700 (Bold)	16px	24px	立即抽取
Caption - 辅助/注释	Lato	400 (Regular)	14px	20px	稀有度: SSR
Input - 表单输入	Lato	400 (Regular)	16px	24px	请输入用户名
1.4 图标系统 (Iconography)

风格定义: 放弃通用线条图标。采用**“圆润描边 (Chubby Stroke)”**风格。所有图标应具有 2px 的描边宽度和圆形的端点/拐角（stroke-linecap: round, stroke-linejoin: round），营造可爱、柔软的质感。

图标清单与设计要求:

主页/商店 (Home/Store): 可爱的小房子形状。

收藏 (Collection): 打开的宠物图鉴/书本。

玩家秀 (Showcase): 照相机或画廊图标。

设置 (Settings): 齿轮，但齿牙要圆润。

登录/用户 (User): 简化的小宠物头像轮廓。

登出 (Logout): 一个打开的门，里面有向外的箭头。

点赞 (Like): 空心爱心，点击后填充为珊瑚粉并有动画。

分享 (Share): 纸飞机，但线条圆润。

搜索 (Search): 放大镜。

关闭 (Close): "X" 形状，但端点是圆的。

箭头 (Arrows): 用于轮播和排序，箭头形状要柔和。

1.5 UI 元素风格指南 (Component Bible)

卡片 (Cards - BaseCard.vue):

圆角: border-radius: 16px;

边框: 默认 1px solid #EAEAEA。对于稀有宠物卡片，边框替换为对应稀有度的颜色。

阴影 (Box Shadow): 使用双层阴影创造柔和的深度感。

box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05), 0px 1px 3px rgba(0, 0, 0, 0.1);

悬浮 (Hover) 状态:

transform: translateY(-4px);

box-shadow: 0px 8px 12px rgba(0, 0, 0, 0.07), 0px 3px 6px rgba(0, 0, 0, 0.12);

transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);

按钮 (Buttons - BaseButton.vue):

主按钮 (Primary):

背景色: #FF6F61 (珊瑚粉)

字体颜色: #FFFFFF (白色)

圆角: border-radius: 12px;

内边距: padding: 12px 24px;

悬浮: 背景变亮 #FF8B82。

点击: 背景变深 #E66357，并有 transform: scale(0.98); 的效果。

次要按钮 (Secondary):

背景色: transparent

边框: 1px solid #FF6F61

字体颜色: #FF6F61

悬浮: 背景变为极浅的粉色 #FFF0F0。

禁用状态 (Disabled):

背景色: #EAEAEA

字体颜色: #AAAAAA

cursor: not-allowed;

模态框 (Modals - BaseModal.vue):

背景遮罩层: background-color: rgba(0, 0, 0, 0.4);，带有 backdrop-filter: blur(4px); 以实现毛玻璃效果。

模态框容器: background-color: #FFFFFF;，border-radius: 20px;，拥有和卡片类似的阴影。

出现/消失动画: 从 scale(0.95) 和 opacity(0) 过渡到 scale(1) 和 opacity(1)。

输入框 (Inputs - BaseInput.vue):

背景色: #F9F9F9

边框: 1px solid #EAEAEA

圆角: border-radius: 10px;

聚焦 (Focus) 状态: 边框变为 #FF6F61，并带有轻微的 box-shadow: 0 0 0 3px rgba(255, 111, 97, 0.2);。

错误 (Error) 状态: 边框变为错误红色 #dc3545。

1.6 核心美术资产监制指南
1.6.1 宠物设计哲学

美术风格: 统一采用**“现代扁平 Q 版 (Modern Flat Chibi)”**风格。

特征: 大头、大眼、小身体。五官简化但极富表情。

线条: 使用干净、粗细均匀的矢量线条，无毛刺。

上色: 大面积使用纯色色块，用简单的软阴影（非渐变）或硬阴影（赛璐璐风格）来表现体积感，避免复杂的材质和光影。

进化路径 (Evolution Path) - [关键设计]

幼体 (Baby Form):

设计核心: 极致的“萌”。

形态: 身体比例更趋近于 1:1（头：身），形态圆润，四肢短小。

表情: 眼睛占据面部较大比例，表情通常是天真、好奇或瞌睡。

设计元素: 仅保留最核心的物种特征，去除复杂纹理和装饰。

示例: 一只幼体小火龙，可能只是一个红色的、带小尾巴的圆球，头上有一个小小的火苗。

成年体 (Adult Form):

设计核心: “酷”与“个性”。

形态: 身体比例拉长至 1:1.5 或 1:2，姿态更有动感，展现出个性。

表情: 眼神更锐利或更温柔，表情能反映其性格（如勇敢、优雅、调皮）。

设计元素: 增加更多细节。如盔甲、饰品、更复杂的斑纹、更华丽的鬃毛/尾巴。

示例: 成年体火龙，身体更修长，眼神坚毅，身上有简单的鳞片纹理，尾巴上的火焰更旺盛，甚至带有粒子效果。

稀有度与视觉表现 (Rarity & Visuals)

N/R 宠物: 设计简洁，配色方案简单（不超过3种主色）。

SR 宠物: 设计上开始出现独特的饰品或更复杂的纹理。配色更丰富。可能有简单的静态姿势特效（如脚下有小光环）。

SSR 宠物: 拥有独特且复杂的动态姿势。设计上包含发光元素、半透明材质或独特的动态粒子效果（如身边漂浮着星尘、火焰或花瓣）。拥有专属的卡面背景。

UR 宠物: 全屏动态卡面。宠物本身可能是 Live2D 或 Spine 动画。拥有极致华丽的粒子特效和背景。

1.6.2 场景与背景设计

风格: 采用**“绘本水彩 (Picture-Book Watercolor)”**风格。

特征: 边缘柔和，色彩之间有自然的浸染和融合感。笔触感可以保留，但不能杂乱。

应用:

登录/注册页: 一幅宁静的、广阔的草地场景，远方有天空和云朵，几只宠物的剪影在玩耍。

盲盒商店背景: 模拟一个温馨的室内宠物店，有木质货架和柔和的灯光。

“我的收藏”背景: 一个简约、干净的陈列室背景。

1.6.3 动画与动效监制 (Motion Design)

核心原则: 动画必须服务于**“情感反馈”和“流程引导”**。

缓动函数 (Easing Function): 全局默认使用 cubic-bezier(0.4, 0, 0.2, 1)，它提供了平滑的加速和减速。对于需要“弹性”或“趣味性”的动画，使用 cubic-bezier(0.34, 1.56, 0.64, 1)。

关键动画序列详述 - [可直接交付给动效设计师或前端]

开盒动画 (The Gacha Sequence) - 耗时约 4-6 秒:

触发 (0.0s): 用户点击“确认抽取”。当前页面内容模糊并变暗 (filter: blur(8px) brightness(0.6)), 过渡 0.3s。

盲盒登场 (0.3s - 1.0s): 一个3D渲染的盲盒模型从屏幕上方快速落下，带有运动模糊。在 1.0s 时刻，“咚”地一声砸在屏幕中央，屏幕有轻微的震动效果 (animation: screenShake 0.2s)。盒子落地后会轻微晃动几下然后静止。

能量聚集 (1.0s - 2.5s): 盒子开始发出微光，光线从缝隙中透出。提示用户“点击开启”。鼠标悬停时，光芒变强，盒子轻微上浮。

开盒爆发 (2.5s): 用户点击。盒子盖子“砰”地一声向上弹开（不是消失，是物理弹开）。

稀有度揭示 (2.6s - 3.5s):

一道强烈的光柱从盒子中冲天而起。光柱的颜色严格遵循1.2节定义的稀有度色板！ 这是最关键的情感调动点。

SSR/UR 特效: 如果是 SSR 或 UR，光柱出现时伴有全屏范围的对应颜色的粒子特效，并有更震撼的音效。

宠物亮相 (3.5s - 4.5s): 光芒散去，一个宠物的剪影在光芒中心浮现、旋转、放大。随后剪影被点亮，露出宠物的“幼体”形态。宠物会做一个可爱的专属登场动画（眨眼、招手等）。

结果展示 (4.5s - ...): 动画定格，一个精心设计的“结果卡片”浮现，包含宠物立绘、名称、稀有度标签，以及“再抽一次”和“放入收藏”的按钮。

点赞动画 (Like Micro-interaction):

点击: 空心图标瞬间填充为珊瑚粉。

放大: 图标从 scale(1) 放大到 scale(1.4)，耗时 0.15s。

迸发: 在达到最大尺寸的同时，从图标中心向四周迸发出 5-7 个五彩纸屑或小爱心粒子。

回弹: 图标从 scale(1.4) 弹性回弹到 scale(1)，耗时 0.3s。

Part 2: 系统架构与技术基石 (The Cornerstone)

V2.0 方案评估与 V3.0 修正：

V2.0 不足： 技术栈选择合理，但缺乏对项目组织、代码规范、协作流程的具体定义。没有明确推荐 ORM 或验证库，也没有提及安全问题。

V3.0 改进： 我们将提供一个企业级的项目结构和开发规范，引入 TypeScript 以增强健壮性，选择 Prisma 作为 ORM，并加入完整的安全考量。

2.1 技术栈最终选型 (Tech Stack)

前端: Vue 3 (使用 Composition API & <script setup>) + Vite + TypeScript + Pinia (状态管理) + TailwindCSS (原子化 CSS) + Vue Router

后端: Node.js + Express + TypeScript + PostgreSQL (数据库) + Prisma (ORM) + Zod (验证)

部署/DevOps: GitHub (代码仓库) + GitHub Actions (CI/CD) + Render (托管平台)

开发工具: VS Code + Volar (Vue) + Prisma Extension + Prettier + ESLint

2.2 前端架构 (Frontend Architecture)
2.2.1 目录结构```

src
├── api/ # API 请求封装 (按模块划分)
│ ├── auth.ts
│ ├── series.ts
│ └── user.ts
├── assets/ # 静态资源 (字体, 图片, 全局CSS)
│ ├── fonts/
│ └── images/
│ └── styles/
│ └── main.css # 全局样式
├── components/ # 全局通用组件
│ ├── base/ # 基础原子组件 (BaseButton.vue, BaseCard.vue)
│ └── layout/ # 布局组件 (Navbar.vue, Footer.vue, AdminLayout.vue)
├── composables/ # Vue Composition API 函数封装
│ ├── useAuth.ts
│ └── useDebounce.ts
├── router/ # 路由配置
│ └── index.ts
├── store/ # Pinia 状态管理
│ ├── auth.ts
│ ├── series.ts
│ └── ui.ts
├── types/ # TypeScript 类型定义
│ ├── api.ts
│ ├── pet.ts
│ └── index.ts
├── utils/ # 通用工具函数 (日期格式化等)
└── views/ # 页面级组件 (路由对应)
├── HomeView.vue
├── StoreView.vue
├── SeriesDetailView.vue
└── admin/
├── DashboardView.vue
└── SeriesManagementView.vue

Generated code
#### **2.2.2 状态管理 (Pinia)**
*   **`auth.ts` (认证 Store):**
    *   `state`: `user: User | null`, `token: string | null`, `status: 'idle' | 'loading' | 'error'`
    *   `getters`: `isAuthenticated: boolean`, `isAdmin: boolean`
    *   `actions`: `login(credentials)`, `register(data)`, `logout()`, `fetchUser()`
*   **`series.ts` (盲盒与宠物 Store):**
    *   `state`: `seriesList: Series[]`, `currentSeries: SeriesDetail | null`, `userPets: UserPet[]`
    *   `actions`: `fetchSeriesList()`, `fetchSeriesDetail(id)`, `drawFromSeries(id)`, `fetchUserPets()`
*   **`ui.ts` (UI状态 Store):**
    *   `state`: `isModalOpen: boolean`, `modalContent: string`, `isPageLoading: boolean`
    *   `actions`: `openModal(content)`, `closeModal()`, `setPageLoading(status)`

### **2.3 后端架构 (Backend Architecture)**

#### **2.3.1 目录结构**
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

src
├── api/ # API 路由定义 (Express Routers)
│ ├── v1/
│ │ ├── auth.routes.ts
│ │ ├── series.routes.ts
│ │ └── admin.routes.ts
│ └── index.ts # 组合所有版本路由
├── config/ # 环境变量与配置
│ └── index.ts
├── controllers/ # 控制器 (处理请求, 调用服务)
│ ├── auth.controller.ts
│ └── series.controller.ts
├── lib/ # 外部库的封装 (如 a...
│ └── prisma.ts
├── middleware/ # Express 中间件
│ ├── auth.middleware.ts # 验证JWT
│ ├── admin.middleware.ts # 验证管理员权限
│ └── validation.middleware.ts # Zod验证中间件
├── services/ # 核心业务逻辑
│ ├── auth.service.ts
│ ├── series.service.ts
│ └── gacha.service.ts # 核心抽卡逻辑
├── types/ # TypeScript 类型定义
└── utils/ # 工具函数 (错误处理, 日志)
├── server.ts # Express 应用入口
prisma/
└── schema.prisma # Prisma 数据模型定义

Generated code
#### **2.3.2 API 设计与响应格式**
*   **版本控制:** 所有 API 路由都以 `/api/v1/` 开头，方便未来升级。
*   **统一响应格式:**
    *   **成功:**
        ```json
        {
          "success": true,
          "data": { ... } // or [] or "ok"
        }
        ```
    *   **失败:**
        ```json
        {
          "success": false,
          "error": {
            "code": "INVALID_INPUT", // 预定义的错误码
            "message": "用户名或密码错误"
          }
        }
        ```

### **2.4 安全策略 (Security)**
*   **认证 (Authentication):**
    *   使用 JWT (JSON Web Tokens)。
    *   **流程:** 用户登录成功后，后端生成一个包含 `userId` 和 `role` 的、有过期时间（如 1 小时）的 `accessToken`，和一个长期有效（如 7 天）的 `refreshToken`。`accessToken` 返回给前端，存储在 Pinia 中。`refreshToken` 存储在前端的 `httpOnly` Cookie 中，用于在 `accessToken` 过期后静默刷新。
*   **授权 (Authorization):**
    *   创建 `auth.middleware.ts` 来校验请求头中的 `Authorization: Bearer <token>`。
    *   创建 `admin.middleware.ts`，它在 `auth.middleware` 之后运行，检查解析出的 `token` 载荷中 `role` 是否为 `'ADMIN'`。
*   **输入验证 (Input Validation):**
    *   所有接收请求体的 API 端点，都必须使用 Zod 进行验证。创建一个 `validation.middleware.ts`，它接收一个 Zod schema，如果验证失败，则直接返回 400 错误和详细的错误信息。
*   **密码安全:**
    *   绝不存储明文密码。使用 `bcrypt` 库对用户密码进行加盐哈希后存储。
*   **其他:**
    *   使用 `cors` 中间件限制可接受的跨域请求来源。
    *   使用 `helmet` 中间件设置各种安全的 HTTP 头，防范 XSS、点击劫持等攻击。
    *   Prisma 本身可以有效防止 SQL 注入。

---

**(下一部分将进入 Part 3，对评分标准中的每一个功能点进行超详细的拆解，这将是文档最庞大的部分。)**
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.


Part 3: 功能蓝图与实现细节 (The Core Blueprint)
我们将逐一攻克评分规则中的所有软件功能要求。每个功能点都将遵循以下结构进行剖析：
用户故事 (User Story): 从用户视角定义需求。
UI/UX 流程与线框图描述: 详细描述用户在界面上的每一步操作和系统的反馈，如同文字版的动态线框图。
前端实现细节 (Frontend Implementation):
相关组件 (Components): 需要创建或使用的 Vue 组件。
状态管理 (Pinia): 涉及的 Pinia store 状态和 actions。
API 通信: 调用哪个 API 端点。
核心逻辑: 关键的业务逻辑或交互实现。
后端实现细节 (Backend Implementation):
API 端点 (Endpoint): 路由定义（方法、路径）。
输入验证 (Validation): 使用 Zod 定义的验证 schema。
控制器 (Controller): 请求处理流程。
服务层 (Service): 核心业务逻辑实现。
数据库交互 (Prisma): 具体的数据库查询和操作。
功能点 1: 多用户注册、登录 (Multi-user Registration & Login)
1.1 用户故事
注册: "作为一名新访客，我希望能够使用一个独特的用户名和密码创建一个新账户，以便能够开始收集宠物并使用所有网站功能。"
登录: "作为一名已注册用户，我希望能够使用我的用户名和密码安全地登录我的账户，以便访问我的宠物收藏和个人数据。"
会话保持: "作为一名已登录用户，我希望在关闭浏览器标签页再重新打开后，仍然保持登录状态，以获得无缝的体验。"
1.2 UI/UX 流程与线框图描述
初始状态: 用户访问网站根路径 (/)。由于未登录，Vue Router 的导航守卫会将其重定向到 /welcome 页面。
欢迎页面 (/welcome):
视觉: 页面背景是 Part 1 中定义的“绘本水彩”风格的宁静草地全屏大图。中央是一个毛玻璃效果的面板，面板上有网站的 Logo（一个可爱的宠物剪影 Logo）。
结构: 面板下方有两个并排的页签按钮：“登录”和“注册”。默认“登录”页签被选中，显示登录表单。“登录”页签按钮是强调色 #FF6F61，“注册”按钮是中度灰色 #666666。
切换到注册: 用户点击“注册”页签。
动效: 表单区域发生平滑的淡入淡出切换，耗时 0.3s。“注册”页签变为强调色，“登录”页签变为灰色。URL hash 变为 /welcome#register。
注册表单交互:
输入框: 包含三个 BaseInput 组件：“用户名”、“密码”、“确认密码”。每个输入框都有圆角和 Part 1 定义的样式。
实时验证:
用户名: 当用户输入时，触发 debounce（防抖，延迟 300ms）的异步验证，向后端发送请求检查用户名是否已存在。输入框右侧会显示一个加载中的小图标。如果已存在，输入框边框变为错误红色 #dc3545，下方显示提示文字：“这个名字已经被别的小伙伴用啦~”。
密码: 实时检查长度（例如，至少8位），并提供一个可点击的“眼睛”图标来切换密码的可见性。
确认密码: 实时检查是否与密码字段的内容一致。如果不一致，边框变红，下方提示：“两次输入的密码不一样哦”。
提交按钮: BaseButton 组件，文字为“立即加入”。当所有验证通过前，按钮处于禁用状态（灰色）。所有验证通过后，按钮变为可用的强调色 #FF6F61。
提交注册: 用户点击“立即加入”按钮。
反馈: 按钮变为加载中状态（例如，按钮内显示一个旋转的小图标），并再次禁用防止重复提交。
API 调用: 向后端 POST /api/v1/auth/register 发送请求。
成功:
弹出一个全局的、成功的 Toast 通知（例如，右上角滑出一个绿色卡片）：“欢迎加入！请登录开始你的旅程吧！”。
表单自动切换回“登录”页签，并且刚刚注册的用户名已经自动填入登录表单的用户名输入框中，密码框自动聚焦，引导用户无缝登录。
失败 (例如，服务器端最终验证用户名重复): 按钮恢复可点击状态。对应的输入框下显示后端返回的错误信息。
登录流程:
用户在登录表单中输入用户名和密码。
点击“进入我的世界”按钮，按钮进入加载状态。
调用 POST /api/v1/auth/login。
成功:
数据存储: 后端返回的 user 信息和 accessToken 被存入 Pinia 的 auth store。refreshToken 被设置为 httpOnly Cookie。
页面过渡: 页面触发全屏过渡动画。例如，欢迎页的背景图向中心旋转、放大并淡出，然后 StoreView 页面淡入。
路由跳转: router.push('/store')。
失败 (密码错误等): 按钮恢复。表单下方显示红色错误提示：“用户名或密码不对哦，请再试一次。”
1.3 前端实现细节 (Frontend)
相关组件:
views/WelcomeView.vue: 整体页面布局。
components/auth/LoginForm.vue: 登录表单逻辑。
components/auth/RegisterForm.vue: 注册表单逻辑。
components/base/BaseInput.vue, components/base/BaseButton.vue: 复用的基础组件。
components/common/Toast.vue: 全局消息通知组件。
状态管理 (Pinia - store/auth.ts):
state: user, accessToken, status
actions:
async function register(payload): 调用 api/auth.ts 中的注册函数，处理加载状态和错误。
async function login(credentials): 调用 API，成功后设置 state.user 和 state.accessToken，并调用 router.push。将 refreshToken 写入 Cookie。
function logout(): 清空 state，清除 Cookie，跳转到 /welcome。
API 通信 (api/auth.ts):
使用 axios 或 fetch 封装。创建 apiClient 实例，自动处理 baseURL 和 Authorization 请求头的添加。
export const registerUser = (data) => apiClient.post('/auth/register', data);
export const loginUser = (credentials) => apiClient.post('/auth/login', credentials);
核心逻辑:
路由守卫 (router/index.ts):
Generated typescript
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const requiresAuth = to.meta.requiresAuth; // 在路由元信息中定义

  if (requiresAuth && !authStore.isAuthenticated) {
    next('/welcome');
  } else {
    next();
  }
});
Use code with caution.
TypeScript
表单验证: 使用 VeeValidate 或 Vuelidate 库可以极大地简化表单状态管理和验证逻辑。
1.4 后端实现细节 (Backend)
API 端点:
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/refresh (用于刷新 accessToken)
GET /api/v1/auth/check-username?username={name} (用于注册时异步校验)
输入验证 (Zod - controllers/auth.controller.ts):
Register Schema:
Generated typescript
const registerSchema = z.object({
  username: z.string().min(3, "用户名至少3个字符").max(20),
  password: z.string().min(8, "密码至少8个字符"),
});
Use code with caution.
TypeScript
Login Schema:
Generated typescript
const loginSchema = z.object({
  username: z.string(),
  password: z.string(),
});
Use code with caution.
TypeScript
控制器 (controllers/auth.controller.ts):
registerHandler: 解析并验证请求体 -> 调用 authService.register -> 返回 201 Created 或错误。
loginHandler: 解析并验证请求体 -> 调用 authService.login -> 成功后，将 refreshToken 设置到 httpOnly Cookie 中，返回包含 accessToken 和 user 信息的 JSON。
服务层 (services/auth.service.ts):
register:
检查用户名是否已存在 (prisma.user.findUnique)。如果存在，抛出业务异常。
使用 bcrypt.hash 对密码进行加盐哈希。
创建一个新用户 (prisma.user.create)。
返回创建成功的用户（不包含密码哈希）。
login:
通过用户名查找用户 (prisma.user.findUnique)。如果用户不存在，抛出异常。
使用 bcrypt.compare 比较请求中的密码和数据库中的哈希。如果不匹配，抛出异常。
生成 accessToken (payload: userId, role, exp: 1h) 和 refreshToken (payload: userId, exp: 7d)。
返回 user, accessToken, refreshToken。
数据库交互 (Prisma - prisma/schema.prisma):
Generated prisma
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  password  String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // ... relations
}

enum UserRole {
  USER
  ADMIN
}
Use code with caution.
Prisma
功能点 5 & 6: 盲盒列表/详情查看 (List/Detail View)
我们将这两个功能点合并，因为它们是用户浏览商品的核心流程。
2.1 用户故事
列表: "作为一名用户，我希望能在一个清晰、美观的页面上看到所有在售的盲盒系列，并且能够快速地通过封面和标题了解它们的主题。"
详情: "作为一名用户，当我看到感兴趣的系列时，我希望能点击进入一个详情页面，查看这个系列包含哪些可能的宠物、它们各自的稀有度，以及抽取消耗的价格，以便我决定是否要进行抽取。"
2.2 UI/UX 流程与线框图描述
访问商店 (/store - List View):
布局: 页面顶部是 Navbar，下方是一个友好的欢迎语（“今天想遇见谁呢？”）和一个 SearchBar（功能点8）。主体内容是一个响应式网格布局，展示多个 SeriesCard 组件。
加载状态:
首次进入页面时，网格中会显示 6-8 个 SeriesCard 的骨架屏版本（Skeleton Card）。骨架屏内部有灰色的、闪烁的占位块，模拟卡片的图片和文字区域。这提供了比空白或旋转图标更好的加载体验。
数据加载完成:
SeriesCard 真实数据被渲染出来，替换掉骨架屏。卡片会以一个轻微的、交错的（staggering）淡入和上移动画出现，增加页面的生动感。
SeriesCard 组件:
严格遵循 Part 1 的卡片设计规范（圆角、阴影）。
内容:
顶部是系列的大尺寸封面图，具有高宽比 aspect-ratio: 4/3。
下方是系列标题（H3 字体样式）。
一个小的标签显示该系列包含的宠物总数（如“共 8 款”）。
交互: 鼠标悬浮时，卡片有上浮和阴影加深的动效。整个卡片是可点击的。
点击卡片进入详情页 (/series/{id} - Detail View):
页面过渡: 列表页的卡片会平滑地过渡到详情页的封面图位置（如果能实现，这是高级的“共享元素过渡”效果）。
布局: 两栏布局。
左栏 (约 40% 宽度):
系列的大尺寸封面图，下方是系列的详细文字介绍，描述了系列的故事背景和设计理念。
右栏 (约 60% 宽度):
系列标题 (H1 字体样式)。
抽取价格（“单次抽取: 100 积分”）。
“卡池预览”模块 (Key Module):
这是一个网格或水平滚动的区域，展示了此系列中所有可能的宠物。
每个宠物以一个小卡片的形式展示，包含宠物的幼体立绘、名字和明确的稀有度标签 (N, R, SR, SSR)。
SSR 级别的宠物卡片会有辉煌流金色的边框，以示尊贵。
为了增加神秘感，可以为 SR/SSR 级别的宠物预览图添加一层半透明的遮罩或问号，鼠标悬浮时才完全显示。
行动区 (Call to Action):
两个醒目的 BaseButton 按钮：“抽取一次” 和 “优惠十连抽”。
“十连抽”按钮上可以有一个小标签，写着“保底一个 R 或以上！”来吸引用户。
2.3 前端实现细节 (Frontend)
相关组件:
views/StoreView.vue: 列表页。
views/SeriesDetailView.vue: 详情页。
components/series/SeriesCard.vue: 列表中的卡片。
components/series/PetPreviewCard.vue: 详情页中用于预览宠物的卡片。
components/common/SkeletonLoader.vue: 骨架屏组件。
状态管理 (Pinia - store/series.ts):
state: seriesList: Series[], seriesListStatus: 'loading' | 'success' | 'error', currentSeries: SeriesDetail | null, currentSeriesStatus: 'loading' | 'success' | 'error'.
actions:
async fetchSeriesList(): 设置 seriesListStatus 为 loading，调用 api/series.ts 中的 getSeriesList，成功后更新 seriesList 和 status。
async fetchSeriesDetail(id): 设置 currentSeriesStatus 为 loading，调用 getSeriesDetail(id)，成功后更新 currentSeries。
API 通信 (api/series.ts):
export const getSeriesList = () => apiClient.get('/series');
export const getSeriesDetail = (id) => apiClient.get(/series/${id});
核心逻辑:
StoreView.vue:
Generated vue
<script setup>
import { onMounted } from 'vue';
import { useSeriesStore } from '@/store/series';

const seriesStore = useSeriesStore();

onMounted(() => {
  if (seriesStore.seriesList.length === 0) { // 防止重复加载
    seriesStore.fetchSeriesList();
  }
});
</script>
<template>
  <div v-if="seriesStore.seriesListStatus === 'loading'">
    <SkeletonLoader v-for="i in 6" :key="i" type="series-card" />
  </div>
  <div v-else-if="seriesStore.seriesListStatus === 'success'">
    <SeriesCard v-for="s in seriesStore.seriesList" :key="s.id" :series="s" />
  </div>
  <div v-else>
    加载失败，请重试...
  </div>
</template>
Use code with caution.
Vue
SeriesDetailView.vue: 使用 useRoute() 获取 URL 中的 id，并在 onMounted 中调用 seriesStore.fetchSeriesDetail(id)。
2.4 后端实现细节 (Backend)
API 端点:
GET /api/v1/series
GET /api/v1/series/:id
控制器 (controllers/series.controller.ts):
getSeriesListHandler: 调用 seriesService.findAll，返回结果。
getSeriesDetailHandler: 从 req.params 中获取 id，调用 seriesService.findById，返回结果。
服务层 (services/series.service.ts):
findAll: 使用 Prisma 查询所有系列。
Generated typescript
return prisma.series.findMany({
  orderBy: { createdAt: 'desc' },
  select: { // 只选择列表页需要的数据，减少传输量
    id: true,
    name: true,
    coverImageUrl: true,
    _count: { // 计算每个系列下的宠物数量
      select: { pets: true }
    }
  }
});
Use code with caution.
TypeScript
findById: 使用 Prisma 查询单个系列的详细信息，并包含其关联的宠物。
Generated typescript
return prisma.series.findUnique({
  where: { id },
  include: { // 关键：使用 include 加载关联数据
    pets: {
      select: {
        id: true,
        name: true,
        rarity: true,
        babyImageUrl: true // 详情页用幼体图
      },
      orderBy: { rarity: 'desc' } // 按稀有度排序
    }
  }
});
Use code with caution.
TypeScript
数据库交互 (Prisma): 查询逻辑如上服务层所示。相关的 schema.prisma 模型将在 Part 4 完整呈现。
(接下来的篇幅将以同等的深度，继续剖析功能点 3: 盲盒抽取、功能点 4: 订单管理、功能点 7: 玩家秀/评论、功能点 8: 搜索，以及功能点 2: 后台管理。)

功能点 3: 盲盒抽取 (The Gacha Draw)
这是应用的核心付费与娱乐功能，其体验的好坏直接决定了用户留存和满意度。
3.1 用户故事
"作为一名用户，当我决定要抽取一个系列的盲盒时，我希望能通过点击一个按钮来完成抽取操作，并立刻看到一个充满惊喜和仪式感的开盒动画，最终揭晓我获得的宠物。"
"作为一名用户，我希望能有‘单抽’和‘十连抽’两种选择，并且‘十连抽’能给我一些优惠或奖励，比如保底获得更高稀有度的宠物。"
3.2 UI/UX 流程与线框图描述
前提: 用户位于盲盒详情页 (/series/{id}), 并且已登录。
交互触发:
用户点击“抽取一次”或“优惠十连抽”按钮。
二次确认模态框 (BaseModal):
屏幕中心弹出一个设计精美的模态框。
标题: "确认抽取"
内容: "即将消耗 [100 积分] 抽取一次【森林系列】盲盒，确定吗？" (内容根据点击的按钮动态生成)。
按钮: 包含两个按钮，一个“我再想想”（次要按钮样式），一个“确认！”（主要按钮样式）。
用户点击“确认！”。
进入开盒序列 (The Gacha Sequence):
模态框消失。整个应用界面（包括导航栏）立即进入全屏动画模式。
此处的动画流程严格遵循 Part 1, 1.6.3 节中定义的“开盒动画”序列，从“盲盒登场”到“结果展示”，每一步都配有专属的音效和视觉特效。这是必须投入前端开发精力的重点。
对于“十连抽”: 动画流程会重复10次，但为了节省时间，可以采用加速模式。前9次开盒动画可以快速播放（例如，每个1.5秒），只显示稀有度光芒和宠物剪影，然后依次排列在屏幕下方。第10次，或者其中保底/最高稀有度的一次，会播放完整版的、充满仪式感的开盒动画。
结果展示:
单抽: 动画结束后，屏幕中央展示一个华丽的 PetResultCard，包含宠物立绘、名称、稀有度等，以及“再抽一次”和“查看我的收藏”按钮。
十连抽: 屏幕上以网格形式展示全部10个抽到的宠物卡片。SSR 级别的卡片会有闪耀的动画效果。下方是“再抽十次”和“全部收下”按钮。
点击“查看我的收藏”或“全部收下”后，全屏动画结束，页面平滑地跳转到 /my-pets 页面，并能看到刚刚获得的新宠物。
3.3 前端实现细节 (Frontend)
相关组件:
views/SeriesDetailView.vue: 发起抽取的页面。
components/gacha/GachaAnimationOverlay.vue: 核心组件。这是一个全局、全屏、高 z-index 的组件，专门负责播放整个开盒动画序列。它通过 Pinia store 控制自身的可见性。
components/gacha/PetResultCard.vue: 用于展示最终抽到的宠物信息。
状态管理 (Pinia):
ui.ts store:
state: isGachaPlaying: boolean, gachaResult: UserPet[] | null
actions: startGacha(), setGachaResult(pets), endGacha()
auth.ts store:
需要一个 user state，其中包含用户的积分 points。
需要一个 action deductPoints(amount) 来在前端乐观更新积分。
API 通信 (api/series.ts):
export const drawFromSeries = (seriesId, amount) => apiClient.post(/series/${seriesId}/draw, { amount });
核心逻辑 (SeriesDetailView.vue):
Generated typescript
// ...
const uiStore = useUiStore();
const seriesStore = useSeriesStore();

async function handleDraw(amount: 1 | 10) {
  // 1. 弹出确认模态框
  const confirmed = await showConfirmationModal(...);
  if (!confirmed) return;

  // 2. 触发开盒动画
  uiStore.startGacha();

  try {
    // 3. 同时发送 API 请求
    const response = await seriesStore.drawFromSeries(seriesId, amount);
    const drawnPets = response.data;

    // 4. 将抽卡结果送入 UI Store，动画组件会监听到这个变化并展示结果
    uiStore.setGachaResult(drawnPets);

    // 5. 乐观更新用户积分 (或在获取用户信息时刷新)
    // authStore.deductPoints(...);
  } catch (error) {
    // 如果 API 失败，结束动画并显示错误信息
    uiStore.endGacha();
    showErrorToast('抽取失败，积分已退回');
  }
}
Use code with caution.
TypeScript
GachaAnimationOverlay.vue 实现:
该组件内部使用 v-if="uiStore.isGachaPlaying" 控制自身显示。
内部状态机：使用一个 ref currentStep 来管理动画的阶段（'falling', 'glowing', 'revealing', 'result'）。
使用 CSS 动画和 setTimeout 或 Promise-based 的定时函数来精确控制动画流程。
监听 uiStore.gachaResult 的变化。当 gachaResult 不为 null 时，从 revealing 阶段过渡到 result 阶段，并渲染 PetResultCard。
3.4 后端实现细节 (Backend)
API 端点: POST /api/v1/series/:seriesId/draw
输入验证 (Zod):
Generated typescript
const drawSchema = z.object({
  amount: z.literal(1).or(z.literal(10)), // 必须是 1 或 10
});
Use code with caution.
TypeScript
控制器 (controllers/series.controller.ts):
drawHandler:
使用 auth.middleware 确保用户已登录，从 req.user 中获取 userId。
验证请求体 (drawSchema)。
从 req.params 获取 seriesId。
调用 gachaService.performDraw(userId, seriesId, amount)。
返回 200 OK 和抽到的宠物数组。
服务层 (gacha.service.ts - 核心业务逻辑):
performDraw:
开启数据库事务: 这是至关重要的一步，确保扣款和发货（创建宠物记录）的原子性。await prisma.$transaction(async (tx) => { ... });
获取数据: 在事务中，获取用户信息（特别是积分）和系列信息（包含抽卡价格和卡池内的宠物及其权重）。
校验余额: 检查 user.points >= series.drawPrice * amount。如果不足，抛出业务异常“积分不足”。
扣除积分: tx.user.update({ where: { id: userId }, data: { points: { decrement: series.drawPrice * amount } } });
执行抽卡算法:
获取该系列的所有宠物及其稀有度/权重。
对于“十连抽”: 执行10次单抽逻辑。实现“保底”机制：如果前9次没有抽出R或以上的宠物，第10次强制从R、SR、SSR卡池中抽取。
单抽算法 (权重随机):
a. 计算总权重 (e.g., N=60, R=25, SR=10, SSR=5, 总权重=100)。
b. 生成一个 1 到总权重之间的随机数。
c. 根据随机数落在哪个权重区间，来确定抽中的稀有度。
d. 从该稀有度的所有宠物中，随机选择一个。
循环 amount 次，将抽中的宠物 petId 存入一个结果数组。
创建宠物记录: 遍历抽卡结果数组，为用户创建新的宠物记录。tx.userPet.createMany({ data: [...] });
返回结果: 查询刚刚创建的 UserPet 记录，并包含其关联的 Pet 信息，然后返回。
数据库交互 (Prisma): 逻辑如上，主要涉及 User 表的更新和 UserPet 表的创建，全部包裹在 $transaction 中。
功能点 4: 盲盒订单管理 (Order Management)
这部分是为了让用户有据可查，增强信任感。在我们的设计中，将“订单”具象化为用户的“抽取历史”。
4.1 用户故事
"作为一名用户，我希望能有一个页面查看我所有的历史抽取记录，包括抽取的时间、花费的积分以及抽到了什么宠物，以便我回顾我的‘战绩’。"
4.2 UI/UX 流程与线框图描述
访问路径: 用户通过个人中心菜单（例如，点击导航栏的用户头像，在下拉菜单中选择“抽取历史”）进入 /my-account/history 页面。
页面布局:
标题：“我的抽取历史”。
主体是一个按时间倒序排列的列表。
列表项设计: 每个列表项代表一次完整的抽取行为（可能是一次单抽，也可能是一次十连抽）。
头部: 显示抽取时间（如“2025年7月25日 15:30”）和操作类型（“单次抽取”或“十连抽取”），以及总花费（“-100 积分”）。
内容: 一个紧凑的网格，展示了这次抽取获得的所有宠物的小头像和稀有度角标。
鼠标悬浮在小头像上时，会显示一个 Tooltip 展示宠物的全名。
4.3 前端实现细节 (Frontend)
相关组件:
views/account/HistoryView.vue: 历史记录页面。
components/history/HistoryRecordItem.vue: 单条历史记录组件。
状态管理: 可以在 auth.ts 或一个新的 account.ts store 中管理。
state: drawHistory: DrawRecord[], historyStatus: 'loading' | ...
actions: fetchDrawHistory()
API 通信 (api/user.ts):
export const getDrawHistory = () => apiClient.get('/me/draw-history');
核心逻辑 (HistoryView.vue):
在 onMounted 中调用 store.fetchDrawHistory()。
使用 v-for 渲染 HistoryRecordItem 组件。
可以添加分页功能，滚动到底部时加载更多历史记录。
4.4 后端实现细节 (Backend)
API 端点: GET /api/v1/me/draw-history
控制器: getUserDrawHistoryHandler
服务层 (services/user.service.ts):
findUserDrawHistory:
这个功能需要一个专门的表来记录“抽取事件”本身，而不是仅仅通过 UserPet 的创建时间来推断。这能更准确地将“十连抽”归为一组。
查询 DrawEvent 表，条件是 userId，按时间倒序。
使用 include 同时加载与该事件关联的所有 UserPet 记录。
数据库设计 (关键补充):
为了实现这个功能，我们需要在 schema.prisma 中增加一个 DrawEvent 模型。
Generated prisma
// 在 Part 4 中会详细给出
model DrawEvent {
  id        String    @id @default(cuid())
  userId    String
  user      User      @relation(fields: [userId], references: [id])
  seriesId  String
  series    Series    @relation(fields: [seriesId], references: [id])
  amount    Int       // 1 or 10
  cost      Int
  createdAt DateTime  @default(now())

  drawnPets UserPet[] // 一个抽取事件关联多个获得的宠物
}

// UserPet 模型需要反向关联
model UserPet {
  // ...
  drawEventId String?
  drawEvent   DrawEvent? @relation(fields: [drawEventId], references: [id])
}
Use code with caution.
Prisma
在 gacha.service.ts 的 performDraw 事务中，除了创建 UserPet，还需要先创建一个 DrawEvent，然后将创建的 UserPet 与这个 DrawEvent 关联起来。

功能点 7: 玩家秀 / 活动评论 (Player Showcase & Comments)
这个功能点在评分规则中提到了两个不同的概念：“玩家秀”和“活动评论”。我们将它们融合并升华，设计成一个统一的、围绕宠物分享和互动的社区中心，我们称之为**“宠物广场 (Pets Plaza)”**。
7.1 用户故事
分享 (玩家秀): "作为一名自豪的用户，当我将一只宠物培养到成年后，我希望可以把它分享到公共的广场上，展示它的最终形态和我的培养成果，并给它写下一段心情或故事，让所有玩家都能看到。"
浏览与互动 (评论): "作为一名社区玩家，我希望能浏览其他人分享的成年宠物，为我喜欢的宠物点赞，并发表我的评论（比如‘太酷了！’或‘好可爱！’），与宠物的主人进行互动。"
7.2 UI/UX 流程与线框图描述
发布入口:
入口不在广场页面，而是在宠物详情页 (/my-pets/{userPetId})。
当一只 UserPet 的状态变为“成年” (status: 'ADULT') 后，该宠物的详情页上会出现一个新的、醒目的“分享到广场”按钮 (BaseButton)。
点击按钮，弹出一个 ShareModal。
模态框内容:
展示即将分享的成年宠物立绘。
一个大的文本区域 (<textarea>)，提示用户“为你的小伙伴写点什么吧...(可选)”，用于填写分享文案。
“确认分享”按钮。
访问宠物广场 (/plaza):
布局: 采用**瀑布流 (Masonry Layout)**布局，以最高效地展示不同尺寸的分享卡片。支持无限滚动加载。
顶部控件: 提供排序选项，如“按最新发布”、“按最热（点赞数最多）”。
分享卡片 (ShowcaseCard.vue):
卡片设计遵循 Part 1 规范，但更注重内容展示。
内容:
顶部是分享的成年宠物大图。
图片下方是宠物主人编写的分享文案。
卡片底部是信息与互动区：
左侧：宠物主人的用户名和头像。
右侧：互动三连 -> 点赞按钮（带数量）、评论按钮（带数量）、分享按钮。
互动流程:
点赞:
用户点击心形图标。如果未登录，提示登录。
如果已登录，图标立即触发 Part 1 中定义的点赞动画，点赞数+1。这是一个乐观更新 (Optimistic Update)，以提供即时反馈。同时向后端发送 POST 请求。如果请求失败，状态回滚。
查看评论 (打开详情):
点击分享卡片本身或评论按钮，不会跳转页面，而是弹出一个全屏详情模态框 (ShowcaseDetailModal.vue)。
模态框布局 (两栏):
左栏: 固定展示被点击卡片的完整内容（宠物大图、文案、主人信息）。
右栏: 评论区。
顶部是一个评论输入框：“发表你的看法...”。
下方是按时间顺序排列的评论列表。
每个评论项包含：评论者头像、用户名、评论内容、评论时间。
发表评论:
用户在右栏的输入框中输入内容，点击“发送”。
评论会立即（乐观更新）出现在评论列表的最上方，并带有“正在发送...”的提示。同时向后端发送 POST 请求。成功后，提示消失。
7.3 前端实现细节 (Frontend)
相关组件:
views/PlazaView.vue: 宠物广场页面，负责瀑布流布局和无限滚动逻辑。
components/showcase/ShareModal.vue: 发布分享的模态框。
components/showcase/ShowcaseCard.vue: 瀑布流中的分享卡片。
components/showcase/ShowcaseDetailModal.vue: 点击卡片后弹出的包含评论区的全屏模态框。
components/showcase/CommentItem.vue: 评论区中的单条评论。
状态管理 (Pinia):
showcase.ts store:
state: posts: ShowcasePost[], currentPage: number, isLoading: boolean, activePost: ShowcasePostDetail | null。
actions: fetchPosts(page), loadMorePosts(), likePost(postId), unlikePost(postId), fetchPostDetail(postId) (用于加载评论), addComment(postId, content)。
API 通信 (api/showcase.ts):
export const getPosts = (page, sortBy) => apiClient.get(/showcase?page=
{sortBy});
export const createPost = (userPetId, content) => apiClient.post('/showcase', { userPetId, content });
export const likePost = (postId) => apiClient.post(/showcase/${postId}/like);
export const unlikePost = (postId) => apiClient.delete(/showcase/${postId}/like);
export const getPostComments = (postId) => apiClient.get(/showcase/${postId}/comments);
export const createComment = (postId, content) => apiClient.post(/showcase/${postId}/comments, { content });
核心逻辑:
瀑布流: 使用 masonry-layout 或类似库。
无限滚动: 监听滚动事件，当滚动到接近底部时，调用 store.loadMorePosts()。
乐观更新: 点赞和评论的实现如 UI/UX 部分所述，是提升用户体验的关键。
7.4 后端实现细节 (Backend)
API 端点:
POST /api/v1/showcase (发布)
GET /api/v1/showcase (获取列表)
GET /api/v1/showcase/:postId/comments (获取评论)
POST /api/v1/showcase/:postId/comments (发表评论)
POST /api/v1/showcase/:postId/like (点赞)
DELETE /api/v1/showcase/:postId/like (取消点赞)
服务层 (services/showcase.service.ts):
createPost:
验证 userPetId 确实属于当前用户，并且状态是 ADULT。
在 ShowcasePost 表中创建新记录。
findAllPosts:
实现分页逻辑。
实现排序逻辑 (orderBy)。
使用 Prisma 的 include 或 select 来聚合每个帖子的点赞数和评论数，避免 N+1 查询。
toggleLike:
使用 userId 和 postId 在 Like 表中查找记录。
如果存在，则删除（取消点赞）。
如果不存在，则创建（点赞）。
数据库设计 (Prisma - Part 4 补充):
Generated prisma
model ShowcasePost {
  id        String    @id @default(cuid())
  content   String?
  createdAt DateTime  @default(now())

  authorId  String
  author    User      @relation(fields: [authorId], references: [id])
  userPetId String    @unique // 一个宠物只能被分享一次
  userPet   UserPet   @relation(fields: [userPetId], references: [id])

  likes     Like[]
  comments  Comment[]
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())

  authorId  String
  author    User     @relation(fields: [authorId], references: [id])
  postId    String
  post      ShowcasePost @relation(fields: [postId], references: [id])
}

model Like {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  userId    String
  user      User     @relation(fields: [userId], references: [id])
  postId    String
  post      ShowcasePost @relation(fields: [postId], references: [id])

  @@unique([userId, postId]) // 联合唯一索引，防止重复点赞
}
Use code with caution.
Prisma
功能点 8: 搜索 (Search)
搜索是发现内容的重要途径。我们将它应用于两个场景：盲盒商店搜索和宠物广场搜索。
8.1 用户故事
"作为用户，我希望能在商店页面通过输入关键词（如‘海洋’、‘幻想’）来快速找到相关主题的盲盒系列。"
"作为用户，我也希望能在宠物广场通过输入宠物的名字或主人的名字来找到特定的分享。"
8.2 UI/UX 流程与线框图描述
商店搜索 (/store):
页面顶部有一个醒目的 SearchBar 组件。
交互:
用户在搜索框中输入文字。
使用 debounce 技术，在用户停止输入 300ms 后，自动触发搜索。
下方的 SeriesCard 网格会平滑地更新为搜索结果。
如果无结果，网格区域会显示一个友好的提示，如“呜...没有找到相关的系列，换个词试试？”并配上一张可爱的插图。
广场搜索 (/plaza):
同样在页面顶部放置一个 SearchBar。
交互流程与商店搜索类似，但下方更新的是瀑布流布局的 ShowcaseCard。
8.3 前端实现细节 (Frontend)
相关组件:
components/common/SearchBar.vue: 一个可复用的搜索框组件。
状态管理:
在 series.ts 和 showcase.ts store 中分别添加 searchQuery: string state。
修改 fetchSeriesList 和 fetchPosts action，让它们可以接受一个 query 参数。
核心逻辑:
在 StoreView.vue 和 PlazaView.vue 中，使用 watch 监听 searchQuery 的变化，当其改变时，调用对应的 fetch action。
SearchBar.vue 组件通过 v-model 与父组件的 searchQuery 双向绑定。
8.4 后端实现细节 (Backend)
API 端点修改:
GET /api/v1/series?search={query}
GET /api/v1/showcase?search={query}
服务层 (series.service.ts & showcase.service.ts):
修改 findAll 方法，接收一个可选的 search 参数。
Prisma 搜索实现:
Generated typescript
// 在 series.service.ts 中
const whereCondition = search
  ? {
      OR: [
        { name: { contains: search, mode: 'insensitive' } }, // 不区分大小写
        { description: { contains: search, mode: 'insensitive' } },
      ],
    }
  : {};

return prisma.series.findMany({
  where: whereCondition,
  // ...
});
Use code with caution.
TypeScript
Generated typescript
// 在 showcase.service.ts 中
const whereCondition = search
  ? {
      OR: [
        { content: { contains: search, mode: 'insensitive' } },
        { author: { username: { contains: search, mode: 'insensitive' } } },
        { userPet: { pet: { name: { contains: search, mode: 'insensitive' } } } }, // 深度搜索
      ],
    }
  : {};
// ...
Use code with caution.
TypeScript
功能点 2: 活动/盲盒管理 (Admin Backend)
这是作业评分的核心工程化要求，体现了项目的完整性。
9.1 用户故事
"作为一名网站管理员，我需要一个独立的、受密码保护的后台系统。在这个系统中，我能够：
创建、查看、编辑和删除盲盒系列。
为一个系列添加、编辑和删除其中包含的宠物，并设置它们的稀有度和图片。
查看所有用户列表和所有订单（抽取事件）流水。"
9.2 UI/UX 流程与线框图描述
访问与布局:
管理员访问 /admin。如果不是管理员角色，会被重定向。
布局: 经典的后台布局。左侧是固定的垂直菜单栏，右侧是内容区域。
菜单栏: Dashboard, 盲盒系列管理, 用户管理, 订单管理, 登出。
盲盒系列管理 (/admin/series):
视图: 一个数据表格 (<table>)，展示所有系列。
列: ID, 封面图预览, 名称, 宠物数量, 创建时间, 操作。
操作列: 包含三个按钮：“管理宠物”, “编辑”, “删除”。
新增: 页面顶部有“新增系列”按钮，点击后弹出一个表单模态框，用于填写系列信息和上传封面图。
管理宠物 (/admin/series/{id}/pets):
点击“管理宠物”按钮后跳转到此页面。
同样是一个数据表格，展示该系列下的所有宠物。
列: ID, 幼体图, 成年体图, 名称, 稀有度, 操作 (“编辑”, “删除”)。
提供“新增宠物”功能，表单中包含名称、稀有度下拉选择、幼体和成年体图片上传。
用户/订单管理:
类似的表格视图，提供搜索和查看功能。
9.3 前端实现细节 (Frontend)
路由: 在 router/index.ts 中设置专门的 /admin 路由组，并添加 meta: { requiresAuth: true, requiresAdmin: true }。
布局组件: components/layout/AdminLayout.vue 包含左侧菜单和右侧的 <router-view>。
页面: views/admin/ 目录下创建所有后台页面。
UI 库: 为了快速开发，后台可以引入一个成熟的 Vue UI 组件库，如 Element Plus 或 Naive UI，它们提供了强大的表格、表单、模态框等组件。
API: 创建一个 api/admin.ts 文件，封装所有后台管理相关的 API 请求。
9.4 后端实现细节 (Backend)
API 路由: 创建 api/v1/admin.routes.ts，所有路由以 /api/v1/admin/ 开头。
中间件: 在整个 admin.routes 上应用 auth.middleware 和 admin.middleware，确保只有管理员能访问。
CRUD 操作:
为 Series 和 Pet 模型编写完整的 CRUD (Create, Read, Update, Delete) 服务和控制器。
图片上传: 需要一个处理 multipart/form-data 的中间件，如 multer。图片上传后可以存储在服务器本地或上传到云存储服务（如 AWS S3, Cloudinary），然后在数据库中保存其 URL。
安全性:
删除操作: 删除操作应设计为“软删除”（设置一个 deletedAt 字段），或者在删除前进行二次确认，以防误操作。

Part 4: 数据库结构设计 (The Data Foundation)
V2.0 方案评估与 V3.0 修正：
V2.0 不足： 仅有零散的模型定义，缺乏关系、枚举、索引和详细注释。没有包含为实现“订单管理”和“玩家秀”等高级功能所必需的模型。
V3.0 改进： 我们将提供一个企业级的、完全规范化的 Prisma Schema。这份 Schema 不仅定义了所有数据表和字段，还明确了它们之间的关系、级联操作、默认值和索引，并为每个模型和关键字段提供了注释，使其本身就是一份清晰的数据库文档。
4.1 schema.prisma 完整代码
将以下内容完整复制到您项目根目录下的 prisma/schema.prisma 文件中。
Generated prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// 1. Prisma Client Generator
// Defines how the Prisma Client is generated (in this case, for TypeScript).
generator client {
  provider = "prisma-client-js"
}

// 2. Datasource Block
// Configures the database connection.
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // Connection string is loaded from .env file.
}

// =====================================================================
// ==                            CORE MODELS                          ==
// =====================================================================

// User Model: Represents a registered user in the system.
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  password  String   // Stored as a hash (e.g., using bcrypt)
  role      UserRole @default(USER)
  points    Int      @default(1000) // Starting points for new users
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // --- Relations ---
  userPets      UserPet[]      // Pets owned by this user
  drawEvents    DrawEvent[]    // Draw history of this user
  showcasePosts ShowcasePost[] // Posts shared by this user
  comments      Comment[]      // Comments made by this user
  likes         Like[]         // Likes given by this user
}

// Enum for User Roles
enum UserRole {
  USER
  ADMIN
}

// Series Model: Represents a collection of pets, i.e., a gacha series.
model Series {
  id              String    @id @default(cuid())
  name            String    @unique
  description     String?
  coverImageUrl   String?
  drawPrice       Int       @default(100) // Price for a single draw
  isActive        Boolean   @default(true) // Allows admins to disable a series without deleting it
  availableFrom   DateTime? // Optional start date for availability
  availableUntil  DateTime? // Optional end date for availability
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // --- Relations ---
  pets       Pet[]       // Pets belonging to this series
  drawEvents DrawEvent[] // Draw events associated with this series
}

// Pet Model: Represents a specific type of pet (the template/blueprint).
model Pet {
  id            String   @id @default(cuid())
  name          String
  rarity        Rarity
  babyImageUrl  String   // Image for the baby form
  adultImageUrl String   // Image for the adult form
  story         String?  // Background story or flavor text
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // --- Relations ---
  seriesId String
  series   Series  @relation(fields: [seriesId], references: [id], onDelete: Cascade) // If a series is deleted, its pets are also deleted.

  ownedBy UserPet[] // Instances of this pet owned by users

  @@unique([seriesId, name]) // A pet name must be unique within a series
}

// Enum for Pet Rarity
enum Rarity {
  N
  R
  SR
  SSR
  UR // For special events
}

// UserPet Model: Represents an instance of a Pet owned by a User. This is the "collectible" item.
model UserPet {
  id          String         @id @default(cuid())
  nickname    String?        // User can give a custom name
  growthValue Int            @default(0)
  maxGrowth   Int            @default(100)
  status      UserPetStatus  @default(BABY)
  lastInteractedAt DateTime? // To limit daily interactions
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // --- Relations ---
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade) // If a user is deleted, their pets are also deleted.

  petId String
  pet   Pet    @relation(fields: [petId], references: [id], onDelete: Restrict) // Prevent deleting a Pet template if it's owned by any user.

  // This links the pet to the specific draw event it came from.
  drawEventId String?
  drawEvent   DrawEvent?     @relation(fields: [drawEventId], references: [id], onDelete: SetNull)

  // A pet can only be shared once. This creates a one-to-one relation with ShowcasePost.
  showcasePost ShowcasePost?

  @@index([userId]) // Index for faster querying of a user's pets
}

// Enum for the status of a user's owned pet.
enum UserPetStatus {
  BABY
  ADULT
}

// =====================================================================
// ==                         FUNCTIONAL MODELS                       ==
// =====================================================================

// DrawEvent Model: Records a single gacha draw action (can be a single or a 10-draw).
// This is our "Order" or "Transaction" model.
model DrawEvent {
  id        String   @id @default(cuid())
  amount    Int      // 1 or 10
  cost      Int      // Total points spent for this event
  createdAt DateTime @default(now())

  // --- Relations ---
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  seriesId String
  series   Series @relation(fields: [seriesId], references: [id], onDelete: Restrict)

  // This is the collection of pets received from this single event.
  drawnPets UserPet[]

  @@index([userId]) // Index for faster querying of user's draw history
}

// ShowcasePost Model: Represents a post in the "Pets Plaza" shared by a user.
model ShowcasePost {
  id        String   @id @default(cuid())
  content   String?  // The user's message/story for the post
  createdAt DateTime @default(now())

  // --- Relations ---
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  // This creates a one-to-one relationship, ensuring a pet instance is shared only once.
  userPetId String  @unique
  userPet   UserPet @relation(fields: [userPetId], references: [id], onDelete: Cascade)

  comments Comment[] // Comments on this post
  likes    Like[]    // Likes for this post

  @@index([authorId])
}

// Comment Model: Represents a comment on a ShowcasePost.
model Comment {
  id        String   @id @default(cuid())
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // --- Relations ---
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  postId String
  post   ShowcasePost @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@index([postId]) // Index for faster retrieval of comments for a post
}

// Like Model: Represents a user's "like" on a ShowcasePost.
model Like {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // --- Relations ---
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  postId String
  post   ShowcasePost @relation(fields: [postId], references: [id], onDelete: Cascade)

  // --- Constraints & Indexes ---
  @@unique([userId, postId]) // A user can only like a post once.
  @@index([postId])
}
Use code with caution.
Prisma
4.2 Schema 设计解析
模型划分:
核心模型 (Core Models): User, Series, Pet, UserPet。这是构成应用基础的四个核心实体。
功能模型 (Functional Models): DrawEvent, ShowcasePost, Comment, Like。这些模型是为实现特定功能（如订单历史、社区）而创建的。
关系 (Relations):
一对多 (One-to-Many): User <-> UserPet, Series <-> Pet, ShowcasePost <-> Comment 等是典型的一对多关系。Prisma 通过在“多”的一方存储外键，并在两边都定义关系字段来处理。
一对一 (One-to-One): UserPet <-> ShowcasePost 是一个一对一关系，通过在 ShowcasePost 中设置一个对 userPetId 的 @unique 约束来实现。这完美地满足了“一只宠物只能被分享一次”的业务规则。
级联删除 (Cascading Deletes):
onDelete: Cascade: 设置在 UserPet 的 userId 关系上，意味着如果一个 User 账户被删除，他所拥有的所有宠物也将被自动删除，保证了数据一致性。
onDelete: Restrict: 设置在 UserPet 的 petId 关系上，这是一个保护性措施。它会阻止删除一个 Pet 模板，如果还有任何用户拥有这个宠物的实例。这可以防止数据孤岛和应用错误。管理员必须先处理掉所有已拥有的实例，才能删除宠物模板。
onDelete: SetNull: 设置在 UserPet 的 drawEventId 关系上。如果一个抽取事件因某些原因被删除，相关的宠物记录不会被删除，只是它们的 drawEventId 字段会变为 null。这是一种折中的数据保留策略。
索引 (Indexes):
我们为经常用作查询条件的字段（如 UserPet 的 userId，Comment 的 postId）添加了 @index 注解。数据库会为这些字段创建索引，极大地提升了 WHERE 查询的性能，尤其是在数据量变大时。
唯一约束 (Unique Constraints):
@unique: 确保了 User 的 username 和 Series 的 name 是唯一的。
@@unique([userId, postId]): 在 Like 模型上使用了复合唯一约束，确保一个用户 (userId) 对一个帖子 (postId) 只能有一条点赞记录，从数据库层面杜绝了重复点赞的脏数据。
4.3 如何使用这份 Schema
保存文件: 将上述代码保存到 prisma/schema.prisma。
安装 Prisma Client: 在终端运行 npm install @prisma/client。
生成数据库迁移文件: 运行 npx prisma migrate dev --name init。这个命令会：
读取你的 schema.prisma 文件。
与数据库当前状态进行比较。
生成一个 SQL 迁移文件，记录了需要对数据库进行的更改（例如 CREATE TABLE ...）。
将这些更改应用到你的数据库。
生成 Prisma Client: 迁移成功后，Prisma 会自动运行 prisma generate 命令，根据你的 Schema 在 node_modules/@prisma/client 中生成一个完全类型化的数据库客户端。
在代码中使用:
Generated typescript
// 在你的后端服务中
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function findUserPets(userId: string) {
  const pets = await prisma.userPet.findMany({
    where: { userId },
    include: { // 类型安全地 include 关联数据
      pet: true,
    },
  });
  return pets;
}
Use code with caution.
TypeScript
(至此，项目的数据地基已牢固搭建。接下来的 Part 5 将聚焦于工程化的另一大支柱——开发活动与 DevOps，确保您的项目从第一行代码到最终上线都遵循最佳实践。)
Part 5: 开发、测试与部署 (The DevOps & Engineering Workflow)
V2.0 方案评估与 V3.0 修正：
V2.0 不足： 提到了 GitHub Actions 和 Render，但没有提供具体的配置文件和操作步骤。对于代码规范、测试等工程化活动描述不足。
V3.0 改进： 我们将提供可直接复制粘贴的 GitHub Actions 配置文件，并详细阐述如何配置代码规范工具、实施测试策略，以及一步步将应用部署到线上。这部分将直接回应评分规则中的所有“开发活动”要求。
5.1 代码规范与质量保证 (Code Quality & Linting)
这一部分直接对应评分规则中的“文件的命名、方法的命名、代码实现是否体现出良好的编码习惯”。
5.1.1 工具链配置
ESLint: 用于静态代码分析，发现 JavaScript/TypeScript 代码中的问题。
Prettier: 一个自动化的代码格式化工具，确保整个项目代码风格一致。
Husky & lint-staged: 在每次 git commit 前自动运行 ESLint 和 Prettier，强制保证入库代码的质量。
5.1.2 实施步骤 (前端 & 后端通用)
安装依赖:
Generated bash
# For both frontend and backend projects
npm install --save-dev eslint prettier eslint-config-prettier eslint-plugin-prettier husky lint-staged
Use code with caution.
Bash
前端额外依赖: npm install --save-dev @typescript-eslint/parser @typescript-eslint/eslint-plugin eslint-plugin-vue
后端额外依赖: npm install --save-dev @typescript-eslint/parser @typescript-eslint/eslint-plugin
配置文件 - .eslintrc.js (以 Vue/TypeScript 前端为例):
Generated javascript
module.exports = {
  root: true,
  env: { node: true, browser: true },
  extends: [
    'plugin:vue/vue3-recommended',
    'eslint:recommended',
    '@vue/typescript/recommended',
    'plugin:prettier/recommended', // 关键：确保 Prettier 规则优先
  ],
  parserOptions: {
    ecmaVersion: 2020,
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // 在这里可以覆盖或添加你自己的规则
  },
};
Use code with caution.
JavaScript
配置文件 - .prettierrc.js:
Generated javascript
module.exports = {
  semi: true, // 句末使用分号
  singleQuote: true, // 使用单引号
  trailingComma: 'all', // 在多行结构的末尾添加逗号
  printWidth: 80, // 每行最大宽度
  tabWidth: 2, // tab 宽度
};
Use code with caution.
JavaScript
配置 Git Hooks (package.json):
Generated json
// In your package.json
"scripts": {
  // ... your other scripts
  "lint": "eslint --ext .ts,.vue src/",
  "format": "prettier --write src/"
},
"husky": {
  "hooks": {
    "pre-commit": "lint-staged"
  }
},
"lint-staged": {
  "*.{js,ts,vue}": [
    "eslint --fix",
    "prettier --write"
  ]
}
Use code with caution.
Json
激活 Husky: 运行 npx husky install。
效果: 现在，每当开发者执行 git commit 时，Husky 会自动对暂存区的文件运行 ESLint 和 Prettier 进行检查和修复。任何无法自动修复的错误都会阻止提交，从而从源头保证代码质量。
5.2 持续集成 (Continuous Integration - CI)
这部分直接对应评分规则中的“是否基于 GitHub Actions 进行了持续集成活动”。
5.2.1 CI 流程设计
我们的 CI 流程将在每次向 main 分支推送或发起 Pull Request 时自动触发。它将执行以下任务：
检出代码 (Checkout Code): 获取最新代码。
设置环境 (Setup Environment): 安装 Node.js 和项目依赖。
代码质量检查 (Lint & Format Check): 运行 ESLint 和 Prettier 检查代码是否合规。
运行测试 (Run Tests): (见 5.3 节) 执行单元测试和集成测试。
构建项目 (Build Project): 运行 npm run build 确保项目可以成功打包。
5.2.2 GitHub Actions 配置文件
在你的项目根目录下创建 .github/workflows/ci.yml 文件。
Generated yaml
# .github/workflows/ci.yml
name: Node.js CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build_and_test:
    runs-on: ubuntu-latest # 使用最新的 Ubuntu runner

    strategy:
      matrix:
        node-version: [18.x] # 指定 Node.js 版本

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm' # 缓存 npm 依赖，加快后续构建速度

    - name: Install dependencies
      run: npm ci # 使用 npm ci 可以确保更可靠、更快速的安装

    - name: Run Linting
      run: npm run lint

    - name: Run Unit & Integration Tests
      run: npm test # 假设你的测试命令是 'npm test'
      # 对于后端，可能需要启动一个测试数据库
      # env:
      #  DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}

    - name: Build project
      run: npm run build```

**效果:** 配置好后，在 GitHub 仓库的 "Actions" 标签页，你将能看到每一次提交触发的自动化工作流，以及每一步的执行结果。这是项目工程化水平的直接体现。

### **5.3 测试策略 (Testing Strategy)**

*   **单元测试 (Unit Tests):**
    *   **目标:** 测试最小的功能单元，如一个独立的 Vue composable 函数或一个后端的 service 函数。
    *   **工具:** 使用 `Vitest` (因为我们用了 Vite) 或 `Jest`。
    *   **示例 (后端 `gacha.service.ts` 的抽卡算法):** 模拟一个卡池数据，多次调用抽卡函数，断言其返回结果的稀有度分布是否大致符合预期。
*   **组件测试 (Component Tests):**
    *   **目标:** 在隔离的环境中测试单个 Vue 组件的渲染和交互。
    *   **工具:** `Vue Testing Library` 或 `@vue/test-utils`。
    *   **示例 (`BaseButton.vue`):** 渲染按钮，模拟点击事件，断言 `click` 事件是否被触发。测试禁用状态下按钮是否不可点击。
*   **端到端测试 (End-to-End Tests):**
    *   **目标:** 模拟真实用户操作，测试完整的业务流程。
    *   **工具:** `Cypress` 或 `Playwright`。
    *   **示例:** 编写一个测试脚本，自动化执行“访问网站 -> 注册 -> 登录 -> 进入商店 -> 抽取盲盒 -> 在收藏中看到新宠物”的完整流程。

### **5.4 持续部署 (Continuous Deployment - CD)**

这部分对应评分规则中的“最终的软件是否可在生产环境运行”。

我们将使用 **Render** 平台，因为它对 Node.js 应用和静态网站的部署非常友好，并能与 GitHub 无缝集成。

#### **5.4.1 部署后端 (Node.js/Express App)**
1.  **注册 Render 账户** 并连接到你的 GitHub 仓库。
2.  在 Render Dashboard，点击 **"New" -> "Web Service"**。
3.  选择你的后端项目仓库。
4.  **配置服务:**
    *   **Name:** `pet-gacha-backend` (或你喜欢的名字)
    *   **Region:** 选择离你用户最近的区域。
    *   **Branch:** `main`
    *   **Build Command:** `npm install && npm run build` (如果你的 TS 项目需要编译) 或 `npm install` (如果不需要)。
    *   **Start Command:** `node dist/server.js` (编译后的入口) 或 `node src/server.js`。
    *   **Environment Variables:** **关键步骤**。点击 "Advanced"，添加你在 `.env` 文件中定义的所有环境变量，特别是 `DATABASE_URL` (使用 Render 提供的 PostgreSQL 数据库的连接串) 和 `JWT_SECRET` (生成一个强随机字符串)。
5.  点击 **"Create Web Service"**。Render 会自动拉取代码，执行构建和启动命令。

#### **5.4.2 部署前端 (Vue/Vite App)**
1.  在 Render Dashboard，点击 **"New" -> "Static Site"**。
2.  选择你的前端项目仓库。
3.  **配置服务:**
    *   **Name:** `pet-gacha-frontend`
    *   **Branch:** `main`
    *   **Build Command:** `npm install && npm run build`
    *   **Publish Directory:** `dist` (Vite 默认的构建输出目录)
4.  **配置重写和代理 (关键步骤):**
    *   为了让前端的路由（如 `/store/{id}`）在刷新时也能正常工作，并让前端 API 请求能正确地指向后端，需要添加规则。
    *   在 "Redirects/Rewrites" 标签下，添加：
        *   **Source:** `/api/*`
        *   **Destination:** `https://<你的后端服务地址>.onrender.com/api/*` (配置 API 代理)
        *   **Type:** `Rewrite`
    *   再添加一条规则：
        *   **Source:** `/*`
        *   **Destination:** `/index.html` (处理所有未匹配到的路径，交给 Vue Router)
        *   **Type:** `Rewrite`
5.  点击 **"Create Static Site"**。

**自动部署效果:** 现在，每当你向 `main` 分支推送新的提交，GitHub Actions 会首先运行 CI 流程。CI 成功后，Render 会自动检测到仓库的变化，并拉取最新代码，重新部署你的前端和后端服务。你实现了完全自动化的从代码提交到线上更新的 DevOps 闭环。

---

## **Part 6: 项目路线图与里程碑 (The Roadmap)**

将庞大的项目分解为可管理的阶段，是成功的关键。

*   **里程碑 1: 核心架构与用户系统 (预计 1 周)**
    *   [ ] 完成前后端项目初始化，配置好 TypeScript, ESLint, Prettier。
    *   [ ] 完成 Part 4 的 Prisma Schema 设计并成功迁移数据库。
    *   [ ] **完成功能点 1: 用户注册与登录。**
    *   [ ] 搭建好 CI/CD 基础流程，实现首次手动部署到 Render。

*   **里程碑 2: 核心浏览与抽取功能 (预计 2 周)**
    *   [ ] **完成功能点 5 & 6: 盲盒列表与详情页。**
    *   [ ] **完成功能点 3: 盲盒抽取核心逻辑 (后端算法与前端交互)。**
    *   [ ] **投入精力重点打磨“开盒动画”。**
    *   [ ] **完成功能点 4: 订单（抽取历史）管理。**

*   **里程碑 3: 社区与管理功能 (预计 2 周)**
    *   [ ] **完成功能点 7: 宠物广场 (玩家秀与评论)。**
    *   [ ] **完成功能点 8: 搜索功能。**
    *   [ ] **完成功能点 2: 后台管理系统 (系列的 CRUD, 宠物的 CRUD)。**
    *   [ ] 编写单元测试和组件测试，达到基本覆盖率。

*   **里程碑 4: 打磨与交付 (预计 1 周)**
    *   [ ] 全面测试所有功能，修复 Bug。
    *   [ ] 优化性能，特别是图片加载和数据库查询。
    *   [ ] 完善 UI/UX 细节，确保所有动画流畅、反馈及时。
    *   [ ] 编写项目文档和最终的作业报告。
    *   [ ] 进行最终的端到端测试。

---

**这份 V3.0 (泰坦版) 计划书到此已全部完成。**

它为您提供了一个从顶层哲学到具体代码实现，从艺术监制到工程运维的全方位、立体化的开发指南。严格遵循这份文档，您不仅能出色地完成您的课程项目，满足所有评分标准，更能构建一个真正高质量、技术扎实、体验卓越的 Web 应用。

祝您开发顺利！
