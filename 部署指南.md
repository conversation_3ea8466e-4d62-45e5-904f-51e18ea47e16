# 🚀 MystiBox 部署指南

## 📋 部署选项

### 1. 本地开发部署
适合开发和测试环境

### 2. 云服务器部署
适合生产环境，如阿里云、腾讯云、AWS等

### 3. 容器化部署
使用Docker进行容器化部署

## 🖥️ 本地开发部署

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- Git

### 部署步骤
```bash
# 1. 克隆项目
git clone <your-repository-url>
cd MystiBox

# 2. 安装依赖
npm install
cd backend && npm install && cd ..

# 3. 配置环境变量
cd backend
cp .env.example .env
# 编辑 .env 文件

# 4. 初始化数据库
npx prisma generate
npx prisma db push

# 5. 启动服务
cd ..
npm run dev:all
```

## ☁️ 云服务器部署

### 服务器要求
- Ubuntu 20.04+ / CentOS 7+
- 2GB+ RAM
- 20GB+ 存储空间
- Node.js 16+

### 1. 服务器环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2 (进程管理器)
sudo npm install -g pm2

# 安装Nginx (反向代理)
sudo apt install nginx -y

# 安装Git
sudo apt install git -y
```

### 2. 部署应用

```bash
# 克隆项目
git clone <your-repository-url>
cd MystiBox

# 安装依赖
npm install
cd backend && npm install && cd ..

# 配置生产环境变量
cd backend
cp .env.example .env
nano .env
```

编辑 `.env` 文件：
```env
DATABASE_URL="file:./prod.db"
JWT_SECRET="your-production-jwt-secret-very-long-and-random"
PORT=3003
NODE_ENV="production"
FRONTEND_URL="https://your-domain.com"
```

```bash
# 初始化数据库
npx prisma generate
npx prisma db push

# 构建前端
cd ..
npm run build

# 启动后端服务
cd backend
pm2 start src/index.js --name "mystibox-backend"

# 配置PM2开机自启
pm2 startup
pm2 save
```

### 3. 配置Nginx

创建Nginx配置文件：
```bash
sudo nano /etc/nginx/sites-available/mystibox
```

添加以下配置：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/MystiBox/dist;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3003;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/mystibox /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 4. 配置SSL (可选)

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🐳 Docker部署

### 1. 创建Dockerfile

前端Dockerfile (`Dockerfile.frontend`):
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

后端Dockerfile (`backend/Dockerfile`):
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npx prisma generate

EXPOSE 3003

CMD ["npm", "start"]
```

### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "80:80"
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - DATABASE_URL=file:./prod.db
      - JWT_SECRET=your-production-jwt-secret
      - PORT=3003
      - NODE_ENV=production
    volumes:
      - ./backend/prod.db:/app/prod.db
```

### 3. 部署命令

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🔧 生产环境优化

### 1. 性能优化

```bash
# 启用Gzip压缩 (Nginx)
sudo nano /etc/nginx/nginx.conf
```

添加：
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 安全配置

```bash
# 配置防火墙
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# 隐藏Nginx版本
sudo nano /etc/nginx/nginx.conf
# 添加: server_tokens off;
```

### 3. 监控和日志

```bash
# PM2监控
pm2 monit

# 查看日志
pm2 logs mystibox-backend

# 设置日志轮转
pm2 install pm2-logrotate
```

## 📊 数据库备份

### SQLite备份
```bash
# 备份数据库
cp backend/prod.db backup/prod_$(date +%Y%m%d_%H%M%S).db

# 自动备份脚本
echo '#!/bin/bash
cp /path/to/MystiBox/backend/prod.db /path/to/backup/prod_$(date +%Y%m%d_%H%M%S).db
find /path/to/backup -name "prod_*.db" -mtime +7 -delete' > backup.sh

chmod +x backup.sh

# 添加到定时任务
crontab -e
# 添加: 0 2 * * * /path/to/backup.sh
```

## 🔄 更新部署

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
npm install
cd backend && npm install && cd ..

# 重新构建前端
npm run build

# 重启后端服务
cd backend
pm2 restart mystibox-backend

# 重新加载Nginx
sudo nginx -s reload
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
```bash
sudo lsof -i :3003
sudo kill -9 <PID>
```

2. **权限问题**
```bash
sudo chown -R $USER:$USER /path/to/MystiBox
```

3. **数据库连接失败**
```bash
cd backend
npx prisma db push --force-reset
```

4. **Nginx配置错误**
```bash
sudo nginx -t
sudo systemctl status nginx
```

## 📞 技术支持

如果在部署过程中遇到问题：
1. 检查服务器日志
2. 确认环境变量配置
3. 验证网络和防火墙设置
4. 查看项目文档或创建Issue

---

**祝你部署成功！** 🎉
