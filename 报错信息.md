我进入了项目，发现了下面的一系列问题，先附上console里面的提示信息：
Starting Vue app...
App.vue:35 MystiBox app initialized
main.ts:16 App mounted successfully
main.ts:14 [Vue Router warn]: No match found for location with path "/history"
warn @ vue-router.js?v=387d8ebf:49
resolve @ vue-router.js?v=387d8ebf:2242
(anonymous) @ vue-router.js?v=387d8ebf:1528
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:659
get value @ chunk-FIAHBV72.js?v=387d8ebf:1854
useLink @ vue-router.js?v=387d8ebf:1569
setup @ vue-router.js?v=387d8ebf:1616
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
setupStatefulComponent @ chunk-FIAHBV72.js?v=387d8ebf:10074
setupComponent @ chunk-FIAHBV72.js?v=387d8ebf:10035
mountComponent @ chunk-FIAHBV72.js?v=387d8ebf:7358
processComponent @ chunk-FIAHBV72.js?v=387d8ebf:7324
patch @ chunk-FIAHBV72.js?v=387d8ebf:6838
patchBlockChildren @ chunk-FIAHBV72.js?v=387d8ebf:7194
patchElement @ chunk-FIAHBV72.js?v=387d8ebf:7112
processElement @ chunk-FIAHBV72.js?v=387d8ebf:6971
patch @ chunk-FIAHBV72.js?v=387d8ebf:6826
componentUpdateFn @ chunk-FIAHBV72.js?v=387d8ebf:7550
run @ chunk-FIAHBV72.js?v=387d8ebf:505
runIfDirty @ chunk-FIAHBV72.js?v=387d8ebf:543
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2497
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queuePostFlushCb @ chunk-FIAHBV72.js?v=387d8ebf:2425
queueEffectWithSuspense @ chunk-FIAHBV72.js?v=387d8ebf:9489
baseWatchOptions.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:8369
watch @ chunk-FIAHBV72.js?v=387d8ebf:2059
doWatch @ chunk-FIAHBV72.js?v=387d8ebf:8393
watchEffect @ chunk-FIAHBV72.js?v=387d8ebf:8304
useLink @ vue-router.js?v=387d8ebf:1576
setup @ vue-router.js?v=387d8ebf:1616
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
setupStatefulComponent @ chunk-FIAHBV72.js?v=387d8ebf:10074
setupComponent @ chunk-FIAHBV72.js?v=387d8ebf:10035
mountComponent @ chunk-FIAHBV72.js?v=387d8ebf:7358
processComponent @ chunk-FIAHBV72.js?v=387d8ebf:7324
patch @ chunk-FIAHBV72.js?v=387d8ebf:6838
mountChildren @ chunk-FIAHBV72.js?v=387d8ebf:7072
mountElement @ chunk-FIAHBV72.js?v=387d8ebf:6995
processElement @ chunk-FIAHBV72.js?v=387d8ebf:6960
patch @ chunk-FIAHBV72.js?v=387d8ebf:6826
mountChildren @ chunk-FIAHBV72.js?v=387d8ebf:7072
mountElement @ chunk-FIAHBV72.js?v=387d8ebf:6995
processElement @ chunk-FIAHBV72.js?v=387d8ebf:6960
patch @ chunk-FIAHBV72.js?v=387d8ebf:6826
mountChildren @ chunk-FIAHBV72.js?v=387d8ebf:7072
mountElement @ chunk-FIAHBV72.js?v=387d8ebf:6995
processElement @ chunk-FIAHBV72.js?v=387d8ebf:6960
patch @ chunk-FIAHBV72.js?v=387d8ebf:6826
componentUpdateFn @ chunk-FIAHBV72.js?v=387d8ebf:7470
run @ chunk-FIAHBV72.js?v=387d8ebf:505
setupRenderEffect @ chunk-FIAHBV72.js?v=387d8ebf:7598
mountComponent @ chunk-FIAHBV72.js?v=387d8ebf:7372
processComponent @ chunk-FIAHBV72.js?v=387d8ebf:7324
patch @ chunk-FIAHBV72.js?v=387d8ebf:6838
mountChildren @ chunk-FIAHBV72.js?v=387d8ebf:7072
mountElement @ chunk-FIAHBV72.js?v=387d8ebf:6995
processElement @ chunk-FIAHBV72.js?v=387d8ebf:6960
patch @ chunk-FIAHBV72.js?v=387d8ebf:6826
componentUpdateFn @ chunk-FIAHBV72.js?v=387d8ebf:7470
run @ chunk-FIAHBV72.js?v=387d8ebf:505
setupRenderEffect @ chunk-FIAHBV72.js?v=387d8ebf:7598
mountComponent @ chunk-FIAHBV72.js?v=387d8ebf:7372
processComponent @ chunk-FIAHBV72.js?v=387d8ebf:7324
patch @ chunk-FIAHBV72.js?v=387d8ebf:6838
render2 @ chunk-FIAHBV72.js?v=387d8ebf:8150
mount @ chunk-FIAHBV72.js?v=387d8ebf:6066
app.mount @ chunk-FIAHBV72.js?v=387d8ebf:12323
(anonymous) @ main.ts:14
main.ts:13 [Vue Router warn]: No match found for location with path "/history"
warn @ vue-router.js?v=387d8ebf:49
resolve @ vue-router.js?v=387d8ebf:2242
(anonymous) @ vue-router.js?v=387d8ebf:1528
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:659
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
runIfDirty @ chunk-FIAHBV72.js?v=387d8ebf:542
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2497
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
install @ vue-router.js?v=387d8ebf:2681
use @ chunk-FIAHBV72.js?v=387d8ebf:5990
(anonymous) @ main.ts:13
social.ts:47  GET http://172.20.10.6:3003/api/social/friends 404 (Not Found)
dispatchXhrRequest @ axios.js?v=387d8ebf:1672
xhr @ axios.js?v=387d8ebf:1552
dispatchRequest @ axios.js?v=387d8ebf:2027
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriends @ social.ts:47
loadFriends @ FriendsView.vue:554
await in loadFriends
(anonymous) @ FriendsView.vue:578
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
install @ vue-router.js?v=387d8ebf:2681
use @ chunk-FIAHBV72.js?v=387d8ebf:5990
(anonymous) @ main.ts:13
index.ts:153 API请求错误: 404 {error: '接口不存在', path: '/api/social/friends'}
(anonymous) @ index.ts:153
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriends @ social.ts:47
loadFriends @ FriendsView.vue:554
await in loadFriends
(anonymous) @ FriendsView.vue:578
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
install @ vue-router.js?v=387d8ebf:2681
use @ chunk-FIAHBV72.js?v=387d8ebf:5990
(anonymous) @ main.ts:13
FriendsView.vue:556 加载好友列表失败: AxiosError {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
loadFriends @ FriendsView.vue:556
await in loadFriends
(anonymous) @ FriendsView.vue:578
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
install @ vue-router.js?v=387d8ebf:2681
use @ chunk-FIAHBV72.js?v=387d8ebf:5990
(anonymous) @ main.ts:13
social.ts:59  GET http://172.20.10.6:3003/api/social/friend-requests 404 (Not Found)
dispatchXhrRequest @ axios.js?v=387d8ebf:1672
xhr @ axios.js?v=387d8ebf:1552
dispatchRequest @ axios.js?v=387d8ebf:2027
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriendRequests @ social.ts:59
loadFriendRequests @ FriendsView.vue:563
await in loadFriendRequests
(anonymous) @ FriendsView.vue:579
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
install @ vue-router.js?v=387d8ebf:2681
use @ chunk-FIAHBV72.js?v=387d8ebf:5990
(anonymous) @ main.ts:13
index.ts:153 API请求错误: 404 {error: '接口不存在', path: '/api/social/friend-requests'}
(anonymous) @ index.ts:153
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriendRequests @ social.ts:59
loadFriendRequests @ FriendsView.vue:563
await in loadFriendRequests
(anonymous) @ FriendsView.vue:579
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
install @ vue-router.js?v=387d8ebf:2681
use @ chunk-FIAHBV72.js?v=387d8ebf:5990
(anonymous) @ main.ts:13
FriendsView.vue:565 加载好友请求失败: AxiosError {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
loadFriendRequests @ FriendsView.vue:565
await in loadFriendRequests
(anonymous) @ FriendsView.vue:579
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
install @ vue-router.js?v=387d8ebf:2681
use @ chunk-FIAHBV72.js?v=387d8ebf:5990
(anonymous) @ main.ts:13
vue-router.js?v=387d8ebf:49 [Vue Router warn]: No match found for location with path "/history"
warn @ vue-router.js?v=387d8ebf:49
resolve @ vue-router.js?v=387d8ebf:2242
pushWithRedirect @ vue-router.js?v=387d8ebf:2358
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
2vue-router.js?v=387d8ebf:49 [Vue Router warn]: No match found for location with path "/history"
warn @ vue-router.js?v=387d8ebf:49
resolve @ vue-router.js?v=387d8ebf:2242
(anonymous) @ vue-router.js?v=387d8ebf:1528
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:659
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
runIfDirty @ chunk-FIAHBV72.js?v=387d8ebf:542
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2497
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
social.ts:47  GET http://172.20.10.6:3003/api/social/friends 404 (Not Found)
dispatchXhrRequest @ axios.js?v=387d8ebf:1672
xhr @ axios.js?v=387d8ebf:1552
dispatchRequest @ axios.js?v=387d8ebf:2027
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriends @ social.ts:47
loadFriends @ BoxStationView.vue:498
await in loadFriends
(anonymous) @ BoxStationView.vue:516
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
index.ts:153 API请求错误: 404 {error: '接口不存在', path: '/api/social/friends'}
(anonymous) @ index.ts:153
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriends @ social.ts:47
loadFriends @ BoxStationView.vue:498
await in loadFriends
(anonymous) @ BoxStationView.vue:516
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
BoxStationView.vue:500 加载好友列表失败: AxiosError {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
loadFriends @ BoxStationView.vue:500
await in loadFriends
(anonymous) @ BoxStationView.vue:516
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
boxes.ts:97  GET http://172.20.10.6:3003/api/boxes/stats 500 (Internal Server Error)
dispatchXhrRequest @ axios.js?v=387d8ebf:1672
xhr @ axios.js?v=387d8ebf:1552
dispatchRequest @ axios.js?v=387d8ebf:2027
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getStats @ boxes.ts:97
loadStats @ BoxStationView.vue:489
(anonymous) @ BoxStationView.vue:515
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
index.ts:153 API请求错误: 500 {message: '获取统计信息失败'}
(anonymous) @ index.ts:153
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getStats @ boxes.ts:97
loadStats @ BoxStationView.vue:489
(anonymous) @ BoxStationView.vue:515
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
BoxStationView.vue:491 加载统计信息失败: AxiosError {message: 'Request failed with status code 500', name: 'AxiosError', code: 'ERR_BAD_RESPONSE', config: {…}, request: XMLHttpRequest, …}
loadStats @ BoxStationView.vue:491
await in loadStats
(anonymous) @ BoxStationView.vue:515
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
2vue-router.js?v=387d8ebf:49 [Vue Router warn]: No match found for location with path "/history"
warn @ vue-router.js?v=387d8ebf:49
resolve @ vue-router.js?v=387d8ebf:2242
(anonymous) @ vue-router.js?v=387d8ebf:1528
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:659
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
runIfDirty @ chunk-FIAHBV72.js?v=387d8ebf:542
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2497
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
social.ts:47  GET http://172.20.10.6:3003/api/social/friends 404 (Not Found)
dispatchXhrRequest @ axios.js?v=387d8ebf:1672
xhr @ axios.js?v=387d8ebf:1552
dispatchRequest @ axios.js?v=387d8ebf:2027
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriends @ social.ts:47
loadFriends @ FriendsView.vue:554
await in loadFriends
(anonymous) @ FriendsView.vue:578
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
index.ts:153 API请求错误: 404 {error: '接口不存在', path: '/api/social/friends'}
(anonymous) @ index.ts:153
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriends @ social.ts:47
loadFriends @ FriendsView.vue:554
await in loadFriends
(anonymous) @ FriendsView.vue:578
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
FriendsView.vue:556 加载好友列表失败: AxiosError {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
loadFriends @ FriendsView.vue:556
await in loadFriends
(anonymous) @ FriendsView.vue:578
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
social.ts:59  GET http://172.20.10.6:3003/api/social/friend-requests 404 (Not Found)
dispatchXhrRequest @ axios.js?v=387d8ebf:1672
xhr @ axios.js?v=387d8ebf:1552
dispatchRequest @ axios.js?v=387d8ebf:2027
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriendRequests @ social.ts:59
loadFriendRequests @ FriendsView.vue:563
await in loadFriendRequests
(anonymous) @ FriendsView.vue:579
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
index.ts:153 API请求错误: 404 {error: '接口不存在', path: '/api/social/friend-requests'}
(anonymous) @ index.ts:153
Promise.then
_request @ axios.js?v=387d8ebf:2230
request @ axios.js?v=387d8ebf:2139
Axios.<computed> @ axios.js?v=387d8ebf:2267
wrap @ axios.js?v=387d8ebf:8
getFriendRequests @ social.ts:59
loadFriendRequests @ FriendsView.vue:563
await in loadFriendRequests
(anonymous) @ FriendsView.vue:579
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
FriendsView.vue:565 加载好友请求失败: AxiosError {message: 'Request failed with status code 404', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}
loadFriendRequests @ FriendsView.vue:565
await in loadFriendRequests
(anonymous) @ FriendsView.vue:579
(anonymous) @ chunk-FIAHBV72.js?v=387d8ebf:4948
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
hook.__weh.hook.__weh @ chunk-FIAHBV72.js?v=387d8ebf:4928
flushPostFlushCbs @ chunk-FIAHBV72.js?v=387d8ebf:2474
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2516
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296
vue-router.js?v=387d8ebf:49 [Vue Router warn]: No match found for location with path "/history"
warn @ vue-router.js?v=387d8ebf:49
resolve @ vue-router.js?v=387d8ebf:2242
(anonymous) @ vue-router.js?v=387d8ebf:1528
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:659
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
refreshComputed @ chunk-FIAHBV72.js?v=387d8ebf:648
isDirty @ chunk-FIAHBV72.js?v=387d8ebf:630
runIfDirty @ chunk-FIAHBV72.js?v=387d8ebf:542
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
flushJobs @ chunk-FIAHBV72.js?v=387d8ebf:2497
Promise.then
queueFlush @ chunk-FIAHBV72.js?v=387d8ebf:2411
queueJob @ chunk-FIAHBV72.js?v=387d8ebf:2406
effect2.scheduler @ chunk-FIAHBV72.js?v=387d8ebf:7592
trigger @ chunk-FIAHBV72.js?v=387d8ebf:533
endBatch @ chunk-FIAHBV72.js?v=387d8ebf:591
notify @ chunk-FIAHBV72.js?v=387d8ebf:853
trigger @ chunk-FIAHBV72.js?v=387d8ebf:827
set value @ chunk-FIAHBV72.js?v=387d8ebf:1699
finalizeNavigation @ vue-router.js?v=387d8ebf:2522
(anonymous) @ vue-router.js?v=387d8ebf:2432
Promise.then
pushWithRedirect @ vue-router.js?v=387d8ebf:2400
push @ vue-router.js?v=387d8ebf:2326
navigate @ vue-router.js?v=387d8ebf:1554
callWithErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2289
callWithAsyncErrorHandling @ chunk-FIAHBV72.js?v=387d8ebf:2296
invoker @ chunk-FIAHBV72.js?v=387d8ebf:11296

1．	我登录的是LR账号，却没有管理员界面
请你先再一次总览一遍文件，然后结合报错信息整理一个任务表然后开始修改
