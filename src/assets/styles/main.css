@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  html {
    font-family: 'Lato', sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'N<PERSON>to', sans-serif;
  }
  
  body {
    @apply bg-neutral-bg text-neutral-text-primary;
    background: linear-gradient(135deg, #87CEEB 0%, #FFDAB9 100%);
    min-height: 100vh;
  }
}

/* 组件样式 */
@layer components {
  .glass-card {
    @apply bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl shadow-lg;
  }
  
  .btn-primary {
    @apply bg-accent-500 hover:bg-accent-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
    opacity: 1 !important;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl border border-gray-300 transition-all duration-300;
    opacity: 1 !important;
  }

  .btn-warning {
    @apply bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300;
    opacity: 1 !important;
  }

  .btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300;
    opacity: 1 !important;
  }

  .btn-success {
    @apply bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300;
    opacity: 1 !important;
  }
}