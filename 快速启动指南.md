# 🚀 MystiBox 快速启动指南

## ⚡ 5分钟快速启动

### 1. 环境检查
```bash
# 检查Node.js版本 (需要 >= 16.0.0)
node --version

# 检查npm版本 (需要 >= 8.0.0)
npm --version
```

### 2. 项目初始化
```bash
# 克隆项目 (替换为你的仓库地址)
git clone <your-repository-url>
cd MystiBox

# 安装前端依赖
npm install

# 安装后端依赖
cd backend
npm install
cd ..
```

### 3. 数据库设置
```bash
# 进入后端目录
cd backend

# 创建环境变量文件
echo 'DATABASE_URL="file:./dev.db"
JWT_SECRET="your-super-secret-jwt-key-change-this"
PORT=3003
FRONTEND_URL="http://localhost:3000"' > .env

# 初始化数据库
npx prisma generate
npx prisma db push

# 返回项目根目录
cd ..
```

### 4. 启动服务
```bash
# 方式一：分别启动 (推荐)
# 终端1: 启动后端
cd backend && npm run dev

# 终端2: 启动前端 (新开终端)
npm run dev

# 方式二：一键启动 (如果配置了并发脚本)
npm run dev:all
```

### 5. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:3003
- 数据库管理: `cd backend && npx prisma studio`

## 🎮 测试账户

### 普通用户
- 用户名: `demo`
- 密码: `demo123`

### 管理员
- 用户名: `admin`
- 密码: `admin123456`

## 🔧 常用命令

### Git操作
```bash
# 初次提交
git add .
git commit -m "feat: 初始化MystiBox项目"
git push -u origin main

# 日常提交
git add .
git commit -m "feat: 添加新功能"
git push origin main
```

### 数据库操作
```bash
cd backend

# 查看数据库
npx prisma studio

# 重置数据库
npx prisma db push --force-reset

# 生成客户端
npx prisma generate
```

### 开发调试
```bash
# 前端开发
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run lint         # 代码检查

# 后端开发
cd backend
npm run dev          # 启动开发服务器
npm start           # 启动生产服务器
```

## 🐛 问题排查

### 端口冲突
```bash
# 查看端口占用
lsof -i :3000
lsof -i :3003

# 杀死进程
kill -9 <PID>
```

### 依赖问题
```bash
# 清除缓存重装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 数据库问题
```bash
cd backend
npx prisma db push --force-reset
```

## 📱 功能测试清单

### ✅ 基础功能测试
- [ ] 用户注册/登录
- [ ] 浏览盲盒商店
- [ ] 抽取宠物 (体验开盒动画)
- [ ] 查看我的宠物
- [ ] 宠物喂养和进化
- [ ] 分享宠物到广场
- [ ] 在广场点赞评论
- [ ] 宠物交易功能
- [ ] 好友聊天功能
- [ ] 积分系统测试

### 🎨 UI/动画测试
- [ ] 点赞动画效果
- [ ] 卡片悬浮效果
- [ ] 宠物互动弹跳
- [ ] 开盒动画完整流程
- [ ] 模态框毛玻璃效果
- [ ] 响应式布局

### 🔐 管理功能测试
- [ ] 管理员登录
- [ ] 系列管理功能
- [ ] 用户管理功能

## 🎯 下一步

项目启动成功后，你可以：

1. **体验游戏** - 使用测试账户体验完整功能
2. **自定义内容** - 根据需要修改宠物数据和图片
3. **部署上线** - 配置生产环境并部署
4. **功能扩展** - 添加新的游戏功能

## 📞 获取帮助

如果遇到问题：
1. 查看完整的 [README.md](./README.md) 文档
2. 检查控制台错误信息
3. 确认环境配置是否正确
4. 创建GitHub Issue寻求帮助

---

**祝你使用愉快！** 🎮✨
